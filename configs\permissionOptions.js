const ROLE_LIST = {
    SYSTEM_ADMIN: "SYSTEM_ADMIN",
    HOTEL_ADMIN: "HOTEL_ADMIN",
    HOTEL_ACCOUNTANT: "HOTEL_ACCOUNTANT",
    RECEPTION_ADMIN: "RECEPTION_ADMIN",
    RECEPTION_STAFF: "RECEPTION_STAFF",
    RESTAURANT_ADMIN: "RESTAURANT_ADMIN",
    RESTAURANT_STAFF: "RESTAURANT_STAFF",
    KITCHEN_ADMIN: "KITCHEN_ADMIN",
    KITCHEN_STAFF: "KITCHEN_STAFF",
    HOUSEKEEPING_ADMIN: "HOUSEKEEPING_ADMIN",
    HOUSEKEEPING_STAFF: "HOUSEKEEPING_STAFF",
    VIEW_ONLY: "VIEW_ONLY",
    CURRENT_VIEW_ONLY: "CURRENT_VIEW_ONLY"
};

const MODEL_LIST = {
    HOTEL: "HOTEL",
    R<PERSON><PERSON>: "ROL<PERSON>",
    GST: "GST",
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: "<PERSON>MP<PERSON><PERSON>Y<PERSON>",
    AGENT: "AGENT",
    ID: "ID",
    PAYMENT_MODE: "PAYMENT_MODE",
    PLAN: "PLAN",
    ROOM_CATEGORY: "ROOM_CATEGORY",
    FOOD: "FOOD",
    SERVICE: "SERVICE",
    MISCELLANEOUS: "MISCELLANEOUS",
    ROOM: "ROOM",
    TABLE: "TABLE",
    GUEST: "GUEST",
    BOOKING: "BOOKING",
    BREAKFAST: "BREAKFAST",
    ORDER: "ORDER",
    BILL: "BILL",
    PAYMENT: "PAYMENT",
    LEDGER: "LEDGER",
    YEAR_END: "YEAR_END",
    ACTIVITY_LOG: "ACTIVITY_LOG",
    ERROR_LOG: "ERROR_LOG"
}

const OPERATION_LIST = {
    VIEW: "VIEW",
    CREATE: "CREATE",
    EDIT: "EDIT",
    REMOVE: "REMOVE",
    PRINT: "PRINT",
    EXPORT: "EXPORT",
    RUN: "RUN"
}

const ALL_MODEL_WISE_PERMISSION_LIST = [
    { module: "HOTEL", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "GST", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ROLE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "PLAN", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "AGENT", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ID_CARD", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "PAYMENT_MODE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ROOM_CATEGORY", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "EMPLOYEE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ROOM", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "TABLE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "FOOD", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "SERVICE", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "MISCELLANEOUS", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "ADVANCE_BOOKING", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "BOOKING", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "FOOD_ORDER", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "SERVICE_ORDER", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "MISCELLANEOUS_ORDER", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "BREAKFAST", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "BILL", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "PAYMENT", operations: ["VIEW", "CREATE", "EDIT", "REMOVE"] },
    { module: "REPORT_BREAKFAST", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "REPORT_COLLECTION", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "REPORT_SALE", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "REPORT_CHECKOUT", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "REPORT_POLICE", operations: ["VIEW", "EXPORT", "PRINT"] },
    { module: "ACTIVITY_LOG", operations: ["VIEW", "REMOVE"] },
    { module: "ERROR_LOG", operations: ["VIEW", "REMOVE"] },
    { module: "YEAR_END", operations: ["RUN"] },
    { module: "DB_BACKUP", operations: ["RUN"] }
];

const ALL_PERMISSION_LIST = {
    VIEW_HOTEL: [ROLE_LIST.SYSTEM_ADMIN],
    CREATE_HOTEL: [ROLE_LIST.SYSTEM_ADMIN],
    EDIT_HOTEL: [ROLE_LIST.SYSTEM_ADMIN],
    REMOVE_HOTEL: [ROLE_LIST.SYSTEM_ADMIN],

    VIEW_ROLE: [
        ROLE_LIST.SYSTEM_ADMIN,
        ROLE_LIST.HOTEL_ADMIN
    ],
    CREATE_ROLE: [
        ROLE_LIST.SYSTEM_ADMIN,
        ROLE_LIST.HOTEL_ADMIN
    ],
    EDIT_ROLE: [
        ROLE_LIST.SYSTEM_ADMIN,
        ROLE_LIST.HOTEL_ADMIN
    ],
    REMOVE_ROLE: [
        ROLE_LIST.SYSTEM_ADMIN,
        ROLE_LIST.HOTEL_ADMIN
    ],

    VIEW_GST: [
        ROLE_LIST.SYSTEM_ADMIN,
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],
    CREATE_GST: [ROLE_LIST.SYSTEM_ADMIN],
    EDIT_GST: [ROLE_LIST.SYSTEM_ADMIN],
    REMOVE_GST: [ROLE_LIST.SYSTEM_ADMIN],

    VIEW_EMPLOYEE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY],
    CREATE_EMPLOYEE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],
    EDIT_EMPLOYEE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOTEL_ACCOUNTANT,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    REMOVE_EMPLOYEE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],

    VIEW_AGENT: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_AGENT: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN
    ],
    EDIT_AGENT: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_AGENT: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_ID: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_ID: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN
    ],
    EDIT_ID: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_ID: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_PAYMENT_MODE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_PAYMENT_MODE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN
    ],
    EDIT_PAYMENT_MODE: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_PAYMENT_MODE: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_PLAN: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_PLAN: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN
    ],
    EDIT_PLAN: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_PLAN: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_ROOM_CATEGORY: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_ROOM_CATEGORY: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN
    ],
    EDIT_ROOM_CATEGORY: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_ROOM_CATEGORY: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_FOOD: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF
    ],
    CREATE_FOOD: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN
    ],
    EDIT_FOOD: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN
    ],
    REMOVE_FOOD: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN
    ],

    VIEW_SERVICE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_SERVICE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],
    EDIT_SERVICE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],
    REMOVE_SERVICE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],

    VIEW_MISCELLANEOUS: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_MISCELLANEOUS: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],
    EDIT_MISCELLANEOUS: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],
    REMOVE_MISCELLANEOUS: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN
    ],

    VIEW_ROOM: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_ROOM: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN
    ],
    EDIT_ROOM: [ROLE_LIST.HOTEL_ADMIN],
    REMOVE_ROOM: [ROLE_LIST.HOTEL_ADMIN],

    VIEW_TABLE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_TABLE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF
    ],
    EDIT_TABLE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF
    ],
    REMOVE_TABLE: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF
    ],

    VIEW_GUEST: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_GUEST: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF
    ],
    EDIT_GUEST: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF
    ],
    REMOVE_GUEST: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF
    ],

    VIEW_BOOKING: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_BOOKING: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF
    ],
    EDIT_BOOKING: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF
    ],
    REMOVE_BOOKING: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF
    ],

    VIEW_BREAKFAST: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_BREAKFAST: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    EDIT_BREAKFAST: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    REMOVE_BREAKFAST: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],

    VIEW_ORDER: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_ORDER: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    EDIT_ORDER: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    REMOVE_ORDER: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],

    VIEW_BILL: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_BILL: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    EDIT_BILL: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    REMOVE_BILL: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],

    VIEW_PAYMENT: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_PAYMENT: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    EDIT_PAYMENT: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    REMOVE_PAYMENT: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],

    VIEW_LEDGER: [
        ROLE_LIST.SYSTEM_ADMIN,
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    VIEW_BILL: [
        ROLE_LIST.SYSTEM_ADMIN,
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],

    VIEW_ACTIVITY_LOG: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_ACTIVITY_LOG: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    EDIT_ACTIVITY_LOG: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    REMOVE_ACTIVITY_LOG: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],

    VIEW_ERROR_LOG: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.VIEW_ONLY,
        ROLE_LIST.CURRENT_VIEW_ONLY
    ],
    CREATE_ERROR_LOG: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    EDIT_ERROR_LOG: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ],
    REMOVE_ERROR_LOG: [
        ROLE_LIST.HOTEL_ADMIN,
        ROLE_LIST.RECEPTION_ADMIN,
        ROLE_LIST.RECEPTION_STAFF,
        ROLE_LIST.RESTAURANT_ADMIN,
        ROLE_LIST.RESTAURANT_STAFF,
        ROLE_LIST.KITCHEN_ADMIN,
        ROLE_LIST.KITCHEN_STAFF,
        ROLE_LIST.HOUSEKEEPING_ADMIN,
        ROLE_LIST.HOUSEKEEPING_STAFF
    ]
};


module.exports = { ROLE_LIST, MODEL_LIST, OPERATION_LIST, ALL_MODEL_WISE_PERMISSION_LIST, ALL_PERMISSION_LIST };