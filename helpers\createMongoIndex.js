const mongoose = require('mongoose');


// Create MongoDB Index
const createMongoIndex = async (name) => {
    try {
        switch (name) {
            // GST
            case process.env.INDEX_GST_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('gsts');

                await collection.createIndex(
                    { minTariff: 1, maxTariff: 1 },
                    { name: name }
                );

                break;
            }

            // Role
            case process.env.INDEX_ROLE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('roles');

                await collection.createIndex(
                    { isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_ROLE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('roles');

                await collection.createIndex(
                    { name: "text", description: "text", },
                    { name: name }
                );

                break;
            }

            // Hotel
            case process.env.INDEX_HOTEL_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('hotels');

                await collection.createIndex(
                    { isEnable: 1, name: 1, urlName: 1, urlPlace: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_HOTEL_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('hotels');

                await collection.createIndex(
                    { name: "text", description: "text", },
                    { name: name }
                );

                break;
            }

            // Employee
            case process.env.INDEX_EMPLOYEE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('employees');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_EMPLOYEE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('employees');

                await collection.createIndex(
                    { name: "text", mobile: "text", email: "text", address: "text", },
                    { name: name }
                );

                break;
            }

            // Plan
            case process.env.INDEX_PLAN_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('plans');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_PLAN_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('plans');

                await collection.createIndex(
                    { name: "text", description: "text", },
                    { name: name }
                );

                break;
            }

            // Agent
            case process.env.INDEX_AGENT_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('agents');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_AGENT_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('agents');

                await collection.createIndex(
                    { name: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            // case process.env.DB_AGENT_INDEX_VECTOR: {
            //     const db = mongoose.connection.db;
            //     const collection = db.collection('agents');

            //     // Define the vector search index
            //     const indexDefinition = {
            //         name: name,
            //         type: "vectorSearch",
            //         definition: {
            //             fields: [
            //                 {
            //                     type: "vector",
            //                     path: "embedding",
            //                     numDimensions: 768,
            //                     similarity: "cosine"
            //                 },
            //                 {
            //                     type: "filter",
            //                     path: "hotel"
            //                 },
            //                 {
            //                     type: "filter",
            //                     path: "isEnable"
            //                 }
            //             ]
            //         }
            //     };

            //     // Create the search index
            //     const result = await collection.createSearchIndex(indexDefinition);
            //     break;
            // }

            // Identification
            case process.env.INDEX_ID_CARD_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('identifications');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_ID_CARD_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('identifications');

                await collection.createIndex(
                    { name: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            // Payment mode
            case process.env.INDEX_PAYMENT_MODE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('paymentmodes');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_PAYMENT_MODE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('paymentmodes');

                await collection.createIndex(
                    { name: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            // Room category
            case process.env.INDEX_ROOM_CATEGORY_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('roomcategories');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_ROOM_CATEGORY_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('roomcategories');

                await collection.createIndex(
                    { name: "text", accommodation: "text", tariff: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            // Food 
            case process.env.INDEX_FOOD_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('foods');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_FOOD_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('foods');

                await collection.createIndex(
                    { name: "text", unitPrice: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            // Service
            case process.env.INDEX_SERVICE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('services');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_SERVICE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('services');

                await collection.createIndex(
                    { name: "text", unitPrice: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            // Miscellaneous
            case process.env.INDEX_MISCELLANEOUS_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('miscellaneous');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, name: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_MISCELLANEOUS_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('miscellaneous');

                await collection.createIndex(
                    { name: "text", unitPrice: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            // Room
            case process.env.INDEX_ROOM_HOTEL_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('rooms');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, status: 1, no: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_ROOM_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('rooms');

                await collection.createIndex(
                    { no: "text", accommodation: "text", tariff: "text", extraBedTariff: "text", extraPersonTariff: "text", maxDiscount: "text", status: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            // Table
            case process.env.INDEX_TABLE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('tables');

                await collection.createIndex(
                    { hotel: 1, isEnable: 1, status: 1, no: 1 },
                    { name: name }
                );

                break;
            }

            case process.env.INDEX_TABLE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('tables');

                await collection.createIndex(
                    { no: "text", accommodation: "text", status: "text", description: "text" },
                    { name: name }
                );

                break;
            }

            default:
                break;
        }

        return true;
    } catch (error) {
        throw error;
    }
};

// Check if MongoDB index exists
const checkMongoIndex = async (name) => {
    try {
        switch (name) {
            // GST
            case process.env.INDEX_GST_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('gsts');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Role
            case process.env.INDEX_ROLE_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('roles');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_ROLE_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('roles');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Hotel
            case process.env.INDEX_HOTEL_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('hotels');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_HOTEL_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('hotels');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Employee
            case process.env.INDEX_EMPLOYEE_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('employees');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_EMPLOYEE_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('employees');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Plan
            case process.env.INDEX_PLAN_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('plans');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_PLAN_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('plans');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Agent
            case process.env.INDEX_AGENT_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('tables');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_AGENT_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('tables');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // case process.env.DB_AGENT_INDEX_VECTOR: {
            //     // Get all indexes
            //     const db = mongoose.connection.db;
            //     const collection = db.collection('tables');
            //     const allIndexes = await collection.listSearchIndexes().toArray();

            //     // Find the specific index
            //     const foundIndex = allIndexes.find(idx => idx.name === name);

            //     if (foundIndex) {
            //         return true;
            //     } else {
            //         return false;
            //     }
            // }

            // Identification
            case process.env.INDEX_ID_CARD_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('identifications');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_ID_CARD_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('identifications');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Payment mode
            case process.env.INDEX_PAYMENT_MODE_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('paymentmodes');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_PAYMENT_MODE_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('paymentmodes');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Room category
            case process.env.INDEX_ROOM_CATEGORY_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('roomcategories');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_ROOM_CATEGORY_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('roomcategories');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Food
            case process.env.INDEX_FOOD_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('foods');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_FOOD_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('foods');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Service
            case process.env.INDEX_SERVICE_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('services');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_SERVICE_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('services');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Miscellaneous
            case process.env.INDEX_MISCELLANEOUS_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('miscellaneous');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_MISCELLANEOUS_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('miscellaneous');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Room
            case process.env.INDEX_ROOM_HOTEL_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('rooms');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_ROOM_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('rooms');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            // Table
            case process.env.INDEX_TABLE_FILTER: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('tables');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            case process.env.INDEX_TABLE_TEXT: {
                // Get all indexes
                const db = mongoose.connection.db;
                const collection = db.collection('tables');
                const allIndexes = await collection.indexes();

                // Find the specific index
                const foundIndex = allIndexes.find(idx => idx.name === name);

                if (foundIndex) return true;
                else return false;
            }

            default:
                return false;
        }
    } catch (error) {
        return false;
    }
};

// Drop MongoDB index
const dropMongoIndex = async (name) => {
    try {
        switch (name) {
            // GST
            case process.env.INDEX_GST_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('gsts');
                await collection.dropIndex(name);

                break;
            }

            // Role
            case process.env.INDEX_ROLE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('roles');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_ROLE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('roles');
                await collection.dropIndex(name);

                break;
            }

            // Hotel
            case process.env.INDEX_HOTEL_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('hotels');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_HOTEL_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('hotels');
                await collection.dropIndex(name);

                break;
            }

            // Employee
            case process.env.INDEX_EMPLOYEE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('employees');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_EMPLOYEE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('employees');
                await collection.dropIndex(name);

                break;
            }

            // Plan
            case process.env.INDEX_PLAN_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('plans');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_PLAN_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('plans');
                await collection.dropIndex(name);

                break;
            }

            // Agent
            case process.env.INDEX_AGENT_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('agents');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_AGENT_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('agents');
                await collection.dropIndex(name);

                break;
            }

            // case process.env.DB_AGENT_INDEX_VECTOR: {
            //     const db = mongoose.connection.db;
            //     const collection = db.collection('agents');
            //     await collection.dropSearchIndex(name);

            //     break;
            // }

            // Identification
            case process.env.INDEX_ID_CARD_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('identifications');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_ID_CARD_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('identifications');
                await collection.dropIndex(name);

                break;
            }

            // Payment mode
            case process.env.INDEX_PAYMENT_MODE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('paymentmodes');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_PAYMENT_MODE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('paymentmodes');
                await collection.dropIndex(name);

                break;
            }

            // Room category
            case process.env.INDEX_ROOM_CATEGORY_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('roomcategories');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_ROOM_CATEGORY_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('roomcategories');
                await collection.dropIndex(name);

                break;
            }

            // Food
            case process.env.INDEX_FOOD_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('foods');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_FOOD_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('foods');
                await collection.dropIndex(name);

                break;
            }

            // Service
            case process.env.INDEX_SERVICE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('services');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_SERVICE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('services');
                await collection.dropIndex(name);

                break;
            }

            // Miscellaneous
            case process.env.INDEX_MISCELLANEOUS_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('miscellaneous');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_MISCELLANEOUS_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('miscellaneous');
                await collection.dropIndex(name);

                break;
            }

            // Room
            case process.env.INDEX_ROOM_HOTEL_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('rooms');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_ROOM_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('rooms');
                await collection.dropIndex(name);

                break;
            }

            // Table
            case process.env.INDEX_TABLE_FILTER: {
                const db = mongoose.connection.db;
                const collection = db.collection('tables');
                await collection.dropIndex(name);

                break;
            }

            case process.env.INDEX_TABLE_TEXT: {
                const db = mongoose.connection.db;
                const collection = db.collection('tables');
                await collection.dropIndex(name);

                break;
            }

            default:
                break;
        }

        return true;
    } catch (error) {
        return false;
    }
};

// Initialize MongoDB index (check if exists, create if not)
const initMongoIndex = async () => {
    // GST
    try {
        const index = process.env.INDEX_GST_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Role
    try {
        const index = process.env.INDEX_ROLE_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_ROLE_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Hotel
    try {
        const index = process.env.INDEX_HOTEL_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_HOTEL_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Employee
    try {
        const index = process.env.INDEX_EMPLOYEE_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_EMPLOYEE_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Plan
    try {
        const index = process.env.INDEX_PLAN_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_PLAN_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Agent
    try {
        const index = process.env.INDEX_AGENT_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_AGENT_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // try {
    //     const index = process.env.DB_AGENT_INDEX_VECTOR;
    //     const exists = await checkMongoIndex(index);

    //     if (!exists) {
    //         await createMongoIndex(index);
    //     }
    // } catch (error) {
    //     return false;
    // }

    // Identification
    try {
        const index = process.env.INDEX_ID_CARD_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_ID_CARD_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Payment mode
    try {
        const index = process.env.INDEX_PAYMENT_MODE_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_PAYMENT_MODE_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Room category
    try {
        const index = process.env.INDEX_ROOM_CATEGORY_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_ROOM_CATEGORY_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Food
    try {
        const index = process.env.INDEX_FOOD_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_FOOD_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Service
    try {
        const index = process.env.INDEX_SERVICE_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_SERVICE_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Miscellaneous
    try {
        const index = process.env.INDEX_MISCELLANEOUS_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_MISCELLANEOUS_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Room
    try {
        const index = process.env.INDEX_ROOM_HOTEL_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_ROOM_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    // Table
    try {
        const index = process.env.INDEX_TABLE_FILTER;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    try {
        const index = process.env.INDEX_TABLE_TEXT;
        const exists = await checkMongoIndex(index);

        if (!exists) {
            await createMongoIndex(index);
        }
        else {
            dropMongoIndex(index);
            await createMongoIndex(index);
        }
    } catch (error) {
        return false;
    }

    return true;
};


module.exports = { createMongoIndex, checkMongoIndex, dropMongoIndex, initMongoIndex };