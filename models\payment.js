const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;


const paymentSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    employee: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Employee"
    },
    booking: {
      type: Schema.Types.ObjectId,
      ref: "Booking"
    },
    bill: {
      type: Schema.Types.ObjectId,
      ref: "Bill"
    },
    receiptNo: {
      type: Number,
      default: 0,
      required: true
    },
    amount: {
      type: Number,
      required: true,
      min: 0
    },
    mode: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "PaymentMode"
    },
    particular: {
      type: String,
      default: ""
    },
    isEnable: {
      type: Boolean,
      default: true,
      required: true
    }
  },
  { timestamps: true }
);


module.exports = mongoose.model("Payment", paymentSchema);