const mongoose = require("mongoose");
const Hotel = require("../../../models/hotel");
const Booking = require("../../../models/booking");
const Table = require("../../../models/table");
const Food = require("../../../models/food");
const Service = require("../../../models/service");
const Miscellaneous = require("../../../models/miscellaneous");
const Order = require("../../../models/order");
const { getOneHotelRedis, setOneHotelRedis } = require("../../../helpers/redis/hotel");
const { getOneFoodRedis } = require("../../../helpers/redis/food");
const { getOneServiceRedis } = require("../../../helpers/redis/service");
const { getOneMiscellaneousRedis } = require("../../../helpers/redis/miscellaneous");
const { stringToBoolean } = require("../../../helpers/boolean");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require("../../../helpers/customError");
const { TABLE_STATUS } = require("../../../configs/tableOptions");
const { ORDER_TYPE, ORDER_STATUS } = require("../../../configs/orderOptions");
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "orderMongo.js";


// get all documents of a collection
const mongoOrders = async (hotelId, employeeId, type, status) => {
  const FUNCTION_NAME = "mongoOrders";

  try {
    // get data
    let condition = { 'hotel': new mongoose.Types.ObjectId(hotelId) };

    if (type) {
      if (type.includes(',')) {
        const typeArray = type.split(',').map(object => object.trim().toUpperCase());
        condition['type'] = { $in: typeArray };
      } else {
        condition['type'] = type.trim().toUpperCase();
      }
    }

    if (status) {
      if (status.includes(',')) {
        const statusArray = status.split(',').map(s => s.trim().toUpperCase());
        condition['status'] = { $in: statusArray };
      } else {
        condition['status'] = status.trim().toUpperCase();
      }
    }

    const pipeline = [
      { $match: condition },
      { $unwind: '$tokens' },
      { $sort: { 'tokens.tokenNo': 1 } }
    ];
    const cursor = await Order.aggregate(pipeline).exec();
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchOrder = async (hotelId, employeeId, type, status, startDate, endDate) => {
  const FUNCTION_NAME = "mongoSearchOrder";

  try {
    let condition = {
      'hotel': new mongoose.Types.ObjectId(hotelId),
      'updatedAt': { $gte: new Date(startDate), $lte: new Date(endDate) }
    };

    if (type) {
      if (type.includes(',')) {
        const typeArray = type.split(',').map(object => object.trim().toUpperCase());
        condition['type'] = { $in: typeArray };
      } else {
        condition['type'] = type.trim().toUpperCase();
      }
    }

    if (status) {
      if (status.includes(',')) {
        const statusArray = status.split(',').map(object => object.trim().toUpperCase());
        condition['status'] = { $in: statusArray };
      } else {
        condition['status'] = status.trim().toUpperCase();
      }
    }

    const pipeline = [
      { $match: condition },
      { $unwind: '$tokens' },
      { $sort: { 'updatedAt': 1, 'tokens.tokenNo': 1 } }
    ];
    const cursor = await Order.aggregate(pipeline).exec();
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetOrder = async (hotelId, employeeId, orderId, tokenId) => {
  const FUNCTION_NAME = "mongoGetOrder";

  let pipeline = null;

  try {
    if (!tokenId) {
      pipeline = [
        {
          $match: {
            '_id': new mongoose.Types.ObjectId(orderId),
            'hotel': new mongoose.Types.ObjectId(hotelId)
          }
        },
        { $unwind: '$tokens' },
        { $sort: { 'tokens.tokenNo': 1 } }
      ];
    } else {
      pipeline = [
        {
          $match: {
            '_id': new mongoose.Types.ObjectId(orderId),
            'hotel': new mongoose.Types.ObjectId(hotelId)
          }
        },
        { $unwind: '$tokens' },
        { $match: { 'tokens._id': new mongoose.Types.ObjectId(tokenId) } },
        { $sort: { 'tokens.tokenNo': 1 } }
      ];
    }

    const cursor = await Order.aggregate(pipeline).exec();
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddOrder = async (hotelId, employeeId, orderId, type, booking, tables, employee,
  deliveryDate, deliveryTime, items) => {
  const FUNCTION_NAME = "mongoAddOrder";

  try {
    // get employees data
    let employeesInput = [];

    employeesInput.push(employeeId);

    // get hotel data
    let hotelCursor = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: hotelId, isEnable: true };
      hotelCursor = await Hotel.findOne(condition);
    }
    else {
      hotelCursor = await getOneHotelRedis(hotelId);
    }
    if (!hotelCursor) throw new customError(ERR_MSG.HOTEL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // get order data
    let orderCursor = null;

    if (orderId) {
      const condition = { _id: orderId, hotel: hotelId };
      orderCursor = await Order.findOne(condition);
      if (!orderCursor) throw new customError(ERR_MSG.ORDER_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    }

    // get booking data
    let bookingInput = null;

    if (booking) {
      const condition = { hotel: hotelId, _id: booking };
      const cursor = await Booking.findOne(condition);
      if (!cursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
      const spread = { ...cursor._doc, _id: cursor.id }
      bookingInput = spread._id;
    }

    // get table data
    let tableInput = [];

    if (tables) tableInput = tables;

    // get item data
    let tokenNo = 0;
    let itemInput = [];

    switch (type.trim().toUpperCase()) {
      case ORDER_TYPE.FOOD:
        tokenNo = parseInt(hotelCursor.lastKOTNo) + 1;

        const fetchFoodItems = async () => {
          await Promise.all(
            items.map(async (item) => {
              // get food data
              let spread = null;
              if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
                const condition = { _id: item._id, hotel: hotelId, isEnable: true };
                const cursor = await Food.findOne(condition);
                if (!cursor) throw new customError(ERR_MSG.FOOD_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
                spread = { ...cursor._doc, _id: cursor.id, hotel: cursor.hotel.toString() };
              }
              else {
                spread = await getOneFoodRedis(hotelId, item._id);
              }

              // calculate tax
              let serviceChargePercentage = 0;
              let serviceChargeAmount = 0;
              const foodCGSTPercentage = Number(parseFloat(hotelCursor.foodCGSTPercentage).toFixed(2));
              const foodSGSTPercentage = Number(parseFloat(hotelCursor.foodSGSTPercentage).toFixed(2));
              const foodCGSTAmount = (Number(parseFloat(foodCGSTPercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * parseInt(item.quantity)) / 100;
              const foodSGSTAmount = (Number(parseFloat(foodSGSTPercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * parseInt(item.quantity)) / 100;

              if (tableInput.length === 0) {
                serviceChargePercentage = Number(parseFloat(hotelCursor.serviceChargePercentage).toFixed(2));
                serviceChargeAmount = (Number(parseFloat(serviceChargePercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;
              }

              itemInput.push({
                item: spread._id,
                name: spread.name,
                unitPrice: Number(parseFloat(spread.unitPrice).toFixed(2)),
                quantity: Number(parseInt(item.quantity)),
                foodCGSTPercentage: Number(parseFloat(foodCGSTPercentage).toFixed(2)),
                foodSGSTPercentage: Number(parseFloat(foodSGSTPercentage).toFixed(2)),
                serviceChargePercentage: Number(parseFloat(serviceChargePercentage).toFixed(2)),
                foodCGSTAmount: Number(parseFloat(foodCGSTAmount).toFixed(2)),
                foodSGSTAmount: Number(parseFloat(foodSGSTAmount).toFixed(2)),
                serviceChargeAmount: Number(parseFloat(serviceChargeAmount).toFixed(2)),
                status: ORDER_STATUS.ORDERED
              });
            }
            ));
        };

        await fetchFoodItems();
        break;

      case ORDER_TYPE.SERVICE:
        tokenNo = parseInt(hotelCursor.lastSOTNo) + 1;

        const fetchServiceItems = async () => {
          await Promise.all(
            items.map(async (item) => {
              // get service data
              let spread = null;
              if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
                const condition = { _id: item._id, hotel: hotelId, isEnable: true };
                const cursor = await Service.findOne(condition);
                if (!cursor) throw new customError(ERR_MSG.SERVICE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
                spread = { ...cursor._doc, _id: cursor.id, hotel: cursor.hotel.toString() };
              }
              else {
                spread = await getOneServiceRedis(hotelId, item._id);
              }

              // calculate tax
              const serviceSGSTPercentage = Number(parseFloat(hotelCursor.serviceSGSTPercentage).toFixed(2));
              const serviceCGSTPercentage = Number(parseFloat(hotelCursor.serviceCGSTPercentage).toFixed(2));
              const serviceChargePercentage = Number(parseFloat(hotelCursor.serviceChargePercentage).toFixed(2));
              const serviceCGSTAmount = (Number(parseFloat(serviceCGSTPercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;
              const serviceSGSTAmount = (Number(parseFloat(serviceSGSTPercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;
              const serviceChargeAmount = (Number(parseFloat(serviceChargePercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;

              itemInput.push({
                item: spread._id,
                name: spread.name,
                unitPrice: Number(parseFloat(spread.unitPrice).toFixed(2)),
                quantity: Number(parseInt(item.quantity)),
                serviceCGSTPercentage: Number(parseFloat(serviceCGSTPercentage).toFixed(2)),
                serviceSGSTPercentage: Number(parseFloat(serviceSGSTPercentage).toFixed(2)),
                serviceChargePercentage: Number(parseFloat(serviceChargePercentage).toFixed(2)),
                serviceCGSTAmount: Number(parseFloat(serviceCGSTAmount).toFixed(2)),
                serviceSGSTAmount: Number(parseFloat(serviceSGSTAmount).toFixed(2)),
                serviceChargeAmount: Number(parseFloat(serviceChargeAmount).toFixed(2)),
                status: ORDER_STATUS.ORDERED
              });
            }
            ));
        };

        await fetchServiceItems();
        break;

      case ORDER_TYPE.MISCELLANEOUS:
        tokenNo = parseInt(hotelCursor.lastMOTNo) + 1;

        const fetchMiscellaneousItems = async () => {
          await Promise.all(
            items.map(async (item) => {
              // get miscellaneous data
              let spread = null;
              if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
                const condition = { _id: item._id, hotel: hotelId, isEnable: true };
                const cursor = await Miscellaneous.findOne(condition);
                if (!cursor) throw new customError(ERR_MSG.MISCELLANEOUS_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
                spread = { ...cursor._doc, _id: cursor.id, hotel: cursor.hotel.toString() };
              }
              else {
                spread = await getOneMiscellaneousRedis(hotelId, item._id);
              }

              itemInput.push({
                item: spread._id,
                name: spread.name,
                unitPrice: Number(parseFloat(spread.unitPrice).toFixed(2)),
                quantity: Number(parseInt(item.quantity)),
                status: ORDER_STATUS.ORDERED
              });
            }
            ));
        };

        await fetchMiscellaneousItems();
        break;

      default:
        break;
    }

    // add or update order
    let findPipeline = '';
    if (!orderId) {
      let tokenArray = [];

      tokenArray.push({
        tokenNo: tokenNo,
        employee: employee,
        deliveryDate: deliveryDate,
        deliveryTime: deliveryTime,
        items: itemInput,
        status: ORDER_STATUS.ORDERED
      });

      const addData = new Order({
        hotel: hotelId,
        employees: employeesInput,
        booking: bookingInput,
        tables: tableInput,
        type: type.trim().toUpperCase(),
        tokens: tokenArray,
        status: ORDER_STATUS.ORDERED
      });

      const addObject = await addData.save();
      if (!addObject) throw new customError(ERR_MSG.ORDER_NOT_SAVE, ERR_CODE.INTERNAL);

      findPipeline = [
        {
          $match: {
            '_id': new mongoose.Types.ObjectId(addObject._id),
            'hotel': new mongoose.Types.ObjectId(hotelId)
          }
        },
        { $unwind: '$tokens' },
        { $match: { 'tokens._id': new mongoose.Types.ObjectId(addObject.tokens[0]._id) } }
      ];
    } else {
      let tokenArray = [];
      tokenArray = orderCursor.tokens;

      tokenArray.push({
        tokenNo: tokenNo,
        employee: employee,
        deliveryDate: deliveryDate,
        deliveryTime: deliveryTime,
        items: itemInput,
        status: ORDER_STATUS.ORDERED
      });

      const modObject = await Order.findOneAndUpdate(
        { hotel: hotelId, _id: orderId },
        {
          $set: {
            employees: employeesInput,
            booking: bookingInput,
            tables: tableInput,
            tokens: tokenArray,
            status: ORDER_STATUS.ORDERED
          }
        },
        { new: true }
      );
      if (!modObject) throw new customError(ERR_MSG.ORDER_NOT_SAVE, ERR_CODE.INTERNAL);

      findPipeline = [
        {
          $match: {
            '_id': new mongoose.Types.ObjectId(modObject._id),
            'hotel': new mongoose.Types.ObjectId(hotelId)
          }
        },
        { $unwind: '$tokens' },
        { $match: { 'tokens._id': new mongoose.Types.ObjectId(modObject.tokens[modObject.tokens.length - 1]._id) } }
      ];
    }

    // update last token no in hotel document
    switch (type.trim().toUpperCase()) {
      case ORDER_TYPE.FOOD:
        await Hotel.findByIdAndUpdate(hotelId, { lastKOTNo: tokenNo });

        if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
          const hotelRedis = await getOneHotelRedis(hotelId);
          hotelRedis.lastKOTNo = tokenNo;
          setOneHotelRedis(hotelRedis);
        }

        break;

      case ORDER_TYPE.SERVICE:
        await Hotel.findByIdAndUpdate(hotelId, { lastSOTNo: tokenNo });

        if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
          const hotelRedis = await getOneHotelRedis(hotelId);
          hotelRedis.lastSOTNo = tokenNo;
          setOneHotelRedis(hotelRedis);
        }

        break;

      case ORDER_TYPE.MISCELLANEOUS:
        await Hotel.findByIdAndUpdate(hotelId, { lastMOTNo: tokenNo });

        if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
          const hotelRedis = await getOneHotelRedis(hotelId);
          hotelRedis.lastMOTNo = tokenNo;
          setOneHotelRedis(hotelRedis);
        }

        break;

      default:
        break;
    }

    if (tables) {
      for (const table of tables) {
        await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.ORDERED });
      }
    }

    // find order
    const cursor = await Order.aggregate(findPipeline).exec();
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModOrder = async (hotelId, employeeId, orderId, tokenId, booking, tables, employee, deliveryDate, deliveryTime, items) => {
  const FUNCTION_NAME = "mongoModOrder";

  try {
    // get employees data
    let employeesInput = [];
    employeesInput.push(employeeId);

    // get hotel data
    let hotelCursor = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { _id: hotelId, isEnable: true };
      hotelCursor = await Hotel.findOne(condition);
    }
    else {
      hotelCursor = await getOneHotelRedis(hotelId);
    }
    if (!hotelCursor) throw new customError(ERR_MSG.HOTEL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // get order data
    const orderCondition = { _id: orderId, hotel: hotelId, 'tokens._id': tokenId };
    let orderCursor = await Order.findOne(orderCondition);
    if (!orderCursor) throw new customError(ERR_MSG.ORDER_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const type = orderCursor.type;

    // get booking data
    let bookingInput = null;
    if (booking) {
      const condition = { hotel: hotelId, _id: booking };
      const cursor = await Booking.findOne(condition);
      if (!cursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
      const spread = { ...cursor._doc, _id: cursor.id }
      bookingInput = spread._id;
    }

    // get table data
    let tableInput = [];
    if (tables) tableInput = tables;

    // get item data
    let itemInput = [];
    switch (type.trim().toUpperCase()) {
      case ORDER_TYPE.FOOD:
        const fetchFoodItems = async () => {
          await Promise.all(
            items.map(async (item) => {
              let spread = null;

              if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
                const condition = { hotel: hotelId, _id: item._id, isEnable: true };
                const cursor = await Food.findOne(condition);
                if (!cursor) throw new customError(ERR_MSG.FOOD_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
                spread = { ...cursor._doc, _id: cursor.id, hotel: cursor.hotel.toString() };
              }
              else {
                spread = await getOneFoodRedis(hotelId, item._id);
              }

              // calculate tax
              let serviceChargePercentage = 0;
              let serviceChargeAmount = 0;
              const foodCGSTPercentage = Number(parseFloat(hotelCursor.foodCGSTPercentage).toFixed(2));
              const foodSGSTPercentage = Number(parseFloat(hotelCursor.foodSGSTPercentage).toFixed(2));
              const foodCGSTAmount = (Number(parseFloat(foodCGSTPercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;
              const foodSGSTAmount = (Number(parseFloat(foodSGSTPercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;

              if (tableInput.length === 0) {
                serviceChargePercentage = Number(parseFloat(hotelCursor.serviceChargePercentage).toFixed(2));
                serviceChargeAmount = (Number(parseFloat(serviceChargePercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;
              }

              itemInput.push({
                item: spread._id,
                name: spread.name,
                unitPrice: Number(parseFloat(spread.unitPrice).toFixed(2)),
                quantity: Number(parseInt(item.quantity)),
                foodCGSTPercentage: Number(parseFloat(foodCGSTPercentage).toFixed(2)),
                foodSGSTPercentage: Number(parseFloat(foodSGSTPercentage).toFixed(2)),
                serviceChargePercentage: Number(parseFloat(serviceChargePercentage).toFixed(2)),
                foodCGSTAmount: Number(parseFloat(foodCGSTAmount).toFixed(2)),
                foodSGSTAmount: Number(parseFloat(foodSGSTAmount).toFixed(2)),
                serviceChargeAmount: Number(parseFloat(serviceChargeAmount).toFixed(2)),
                status: item.status
              });
            }));
        }

        await fetchFoodItems();
        break;

      case ORDER_TYPE.SERVICE:
        const fetchServiceItems = async () => {
          await Promise.all(
            items.map(async (item) => {
              let spread = null;

              if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
                const condition = { hotel: hotelId, _id: item._id, isEnable: true };
                const cursor = await Service.findOne(condition);
                if (!cursor) throw new customError(ERR_MSG.SERVICE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
                spread = { ...cursor._doc, _id: cursor.id, hotel: cursor.hotel.toString() };
              }
              else {
                spread = await getOneServiceRedis(hotelId, item._id);
              }

              // calculate tax
              const serviceCGSTPercentage = Number(parseFloat(hotelCursor.serviceCGSTPercentage).toFixed(2));
              const serviceSGSTPercentage = Number(parseFloat(hotelCursor.serviceSGSTPercentage).toFixed(2));
              const serviceCGSTAmount = (Number(parseFloat(serviceCGSTPercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;
              const serviceSGSTAmount = (Number(parseFloat(serviceSGSTPercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;
              const serviceChargePercentage = Number(parseFloat(hotelCursor.serviceChargePercentage).toFixed(2));
              const serviceChargeAmount = (Number(parseFloat(serviceChargePercentage).toFixed(2)) * Number(parseFloat(spread.unitPrice).toFixed(2)) * Number(parseInt(item.quantity))) / 100;

              itemInput.push({
                item: spread._id,
                name: spread.name,
                unitPrice: Number(parseFloat(spread.unitPrice).toFixed(2)),
                quantity: Number(parseInt(item.quantity)),
                serviceCGSTPercentage: Number(parseFloat(serviceCGSTPercentage).toFixed(2)),
                serviceSGSTPercentage: Number(parseFloat(serviceSGSTPercentage).toFixed(2)),
                serviceChargePercentage: Number(parseFloat(serviceChargePercentage).toFixed(2)),
                serviceCGSTAmount: Number(parseFloat(serviceCGSTAmount).toFixed(2)),
                serviceSGSTAmount: Number(parseFloat(serviceSGSTAmount).toFixed(2)),
                serviceChargeAmount: Number(parseFloat(serviceChargeAmount).toFixed(2)),
                status: item.status
              });
            }));
        }

        await fetchServiceItems();
        break;

      case ORDER_TYPE.MISCELLANEOUS:
        const fetchMiscellaneousItems = async () => {
          await Promise.all(
            items.map(async (item) => {
              let spread = null;

              if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
                const condition = { hotel: hotelId, _id: item._id, isEnable: true };
                const cursor = await Miscellaneous.findOne(condition);
                if (!cursor) throw new customError(ERR_MSG.MISCELLANEOUS_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
                spread = { ...cursor._doc, _id: cursor.id, hotel: cursor.hotel.toString() };
              }
              else {
                spread = await getOneMiscellaneousRedis(hotelId, item._id);
              }

              itemInput.push({
                item: spread._id,
                name: spread.name,
                unitPrice: Number(parseFloat(spread.unitPrice).toFixed(2)),
                quantity: Number(parseInt(item.quantity)),
                status: item.status
              });
            }));
        }

        await fetchMiscellaneousItems();
        break;

      default:
        break;
    };

    // update order data
    const modObject = await Order.findOneAndUpdate(
      {
        _id: orderId,
        hotel: hotelId,
        'tokens._id': tokenId
      },
      {
        $set: {
          employees: employeesInput,
          booking: bookingInput,
          tables: tableInput,
          'tokens.$.employee': employee,
          'tokens.$.deliveryDate': deliveryDate,
          'tokens.$.deliveryTime': deliveryTime,
          'tokens.$.items': itemInput,
          'tokens.$.status': ORDER_STATUS.ORDERED,
          status: ORDER_STATUS.ORDERED
        }
      },
      { new: true }
    );
    if (!modObject) throw new customError(ERR_MSG.ORDER_NOT_SAVE, ERR_CODE.INTERNAL);

    for (const table of tableInput) {
      await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.ORDERED });
    }

    const pipeline = [
      {
        $match: {
          '_id': new mongoose.Types.ObjectId(orderId),
          'hotel': new mongoose.Types.ObjectId(hotelId)
        }
      },
      { $unwind: '$tokens' },
      { $match: { 'tokens._id': new mongoose.Types.ObjectId(tokenId) } },
      { $sort: { 'tokens.tokenNo': 1 } }
    ];
    const cursor = await Order.aggregate(pipeline).exec();
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    });

    return spread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelOrder = async (hotelId, employeeId, orderId, tokenId) => {
  const FUNCTION_NAME = "mongoDelOrder";

  try {
    const findPipeline = [
      {
        $match: {
          '_id': new mongoose.Types.ObjectId(orderId),
          'hotel': new mongoose.Types.ObjectId(hotelId)
        }
      },
      { $unwind: '$tokens' },
      { $match: { 'tokens._id': new mongoose.Types.ObjectId(tokenId) } }
    ];
    const cursor = await Order.aggregate(findPipeline).exec();
    if (cursor[0].tokens.status !== ORDER_STATUS.ORDERED) {
      throw new customError(ERR_MSG.ORDER_NOT_DELETE, ERR_CODE.NOT_ALLOWED);
    }
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    });

    const tables = cursor[0].tables;

    // delete token from order db
    const condition = { _id: orderId, hotel: hotelId };
    const modObject = await Order.updateOne(condition, { $pull: { tokens: { _id: tokenId } } });
    if (!modObject) throw new customError(ERR_MSG.ORDER_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

    const tokenPipeline = [
      {
        $match: {
          '_id': new mongoose.Types.ObjectId(orderId),
          'hotel': new mongoose.Types.ObjectId(hotelId)
        }
      },
      { $unwind: '$tokens' }
    ];
    const tokenCursor = await Order.aggregate(tokenPipeline).exec();
    if (tokenCursor.length === 0) {
      const delObject = await Order.deleteOne({ _id: orderId, hotel: hotelId });
      if (!delObject) throw new customError(ERR_MSG.ORDER_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

      // if order is already deleted then empty table in db
      for (const table of tables) {
        await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.EMPTY });
      }
    }

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModOrderStatus = async (hotelId, employeeId, orderId, tokenId, orderItemIds, status) => {
  const FUNCTION_NAME = "modOrderStatus";

  try {
    // get order data
    const orderTokenCondition = { _id: orderId, hotel: hotelId, 'tokens._id': tokenId };
    let orderTokenCursor = await Order.findOne(orderTokenCondition);
    if (!orderTokenCursor) throw new customError(ERR_MSG.ORDER_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // update order item status
    await Promise.all(
      orderItemIds.map(async (itemId) => {
        const condition = {
          _id: orderId,
          hotel: hotelId,
          'tokens._id': tokenId,
          'tokens.items._id': itemId
        };

        await Order.findOneAndUpdate(
          condition,
          { $set: { 'tokens.$[token].items.$[item].status': status } },
          {
            arrayFilters: [
              { 'token._id': tokenId },
              { 'item._id': itemId }
            ],
            new: true
          }
        );
      })
    );

    orderTokenCursor = await Order.findOne(orderTokenCondition);

    let isItemOrdered = false;
    orderTokenCursor.tokens[0].items.map((item) => {
      if (item.status === ORDER_STATUS.ORDERED) isItemOrdered = true;
    });

    // update token status
    if (!isItemOrdered) {
      await Order.findOneAndUpdate(
        {
          _id: orderId,
          hotel: hotelId,
          'tokens._id': tokenId
        },
        { $set: { 'tokens.$[token].status': ORDER_STATUS.DELIVERED } },
        {
          arrayFilters: [
            { 'token._id': tokenId }
          ],
          new: true
        }
      );
    }

    let isTokenOrdered = false;

    // find order
    const orderCondition = { _id: orderId, hotel: hotelId };
    orderCursor = await Order.findOne(orderCondition);

    orderCursor.tokens.map(async (token) => {
      if (token.status === ORDER_STATUS.ORDERED) isTokenOrdered = true;
    });

    // update order status
    if (!isTokenOrdered) {
      await Order.findOneAndUpdate(
        { _id: orderId, hotel: hotelId },
        { $set: { status: ORDER_STATUS.DELIVERED } },
        { new: true }
      );
    }

    // read db
    const pipeline = [
      {
        $match: {
          '_id': new mongoose.Types.ObjectId(orderId),
          'hotel': new mongoose.Types.ObjectId(hotelId)
        }
      },
      { $unwind: '$tokens' },
      { $match: { 'tokens._id': new mongoose.Types.ObjectId(tokenId) } },
      { $sort: { 'tokens.tokenNo': 1 } }
    ];

    const cursor = await Order.aggregate(pipeline).exec();
    const spread = cursor.map((object) => {
      return {
        _id: object._id.toString(),
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        booking: object.booking ? object.booking.toString() : null,
        tables: object.tables ? object.tables.map((table) => table.toString()) : null,
        token: {
          _id: object.tokens._id.toString(),
          tokenNo: object.tokens.tokenNo,
          employee: object.tokens.employee.toString(),
          deliveryDate: object.tokens.deliveryDate,
          deliveryTime: object.tokens.deliveryTime,
          status: object.tokens.status,
          createdAt: object.createdAt,
          updatedAt: object.updatedAt,
          items: object.tokens.items.map(item => ({
            _id: item._id.toString(),
            item: item.item.toString(),
            name: item.name,
            unitPrice: item.unitPrice,
            quantity: item.quantity,
            status: item.status
          }))
        },
        type: object.type,
        status: object.status,
        createdAt: object.createdAt,
        updatedAt: object.updatedAt
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  mongoOrders, mongoSearchOrder, mongoGetOrder, mongoAddOrder,
  mongoModOrder, mongoDelOrder, mongoModOrderStatus
};