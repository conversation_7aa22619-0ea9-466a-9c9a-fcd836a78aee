const { mongoAgents, mongoSearchAgent, mongoGetAgent, mongoAddAgent, mongoModAgent, mongoDelAgent, mongoDelAgents } = require("./mongo/agentMongo");
const { redisAgents, redisSearchAgent, redisGetAgent, redisSetAgent, redisDelAgent } = require("./redis/agentRedis");
const { agentDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { OPERATION_LIST, MODEL_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "agent.js";


// Get all documents of a collection
const agents = async (args, req) => {
  const FUNCTION_NAME = "agents";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  let allObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.AGENT, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      allObjects = await mongoAgents(hotelId, employeeId);
    }
    else {
      allObjects = await redisAgents(hotelId, employeeId);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.AGENT_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await agentDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Search within collection
const searchAgent = async (args, req) => {
  const FUNCTION_NAME = "searchAgent";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const searchKey = args.agentSearchInput ? args.agentSearchInput.trim().toUpperCase() : "";

  let searchObjects = [];

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.AGENT, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // search data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      searchObjects = await mongoSearchAgent(hotelId, employeeId, searchKey);
    }
    else {
      searchObjects = await redisSearchAgent(hotelId, employeeId, searchKey);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.AGENT_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await agentDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const getAgent = async (args, req) => {
  const FUNCTION_NAME = "getAgent";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  let getObject = null;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.AGENT, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // read single data
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      getObject = await mongoGetAgent(hotelId, employeeId, _id);
    }
    else {
      getObject = await redisGetAgent(hotelId, employeeId, _id);
    }

    if (!getObject) throw new customError(ERR_MSG.AGENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.AGENT_GET);

    // return output
    return await agentDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Insert a document into the collection
const addAgent = async (args, req) => {
  const FUNCTION_NAME = "addAgent";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { name, description } = args.agentInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.AGENT, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);

    const _name = name.trim();
    const _description = description ? description.trim() : "";

    // add mongo data
    const addObject = await mongoAddAgent(hotelId, employeeId, _name, _description);

    // add redis data
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetAgent(hotelId, employeeId, addObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.AGENT_ADD);

    // return output
    return await agentDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Modify a document of a collection
const modAgent = async (args, req) => {
  const FUNCTION_NAME = "modAgent";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, name, description } = args.agentInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.AGENT, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);
    if (!name) throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.CONFLICT);

    const _name = name.trim().toUpperCase();
    const _description = description ? description.trim() : "";

    // modify mongo data
    const modObject = await mongoModAgent(hotelId, employeeId, _id, _name, _description);

    // modify redis data
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisSetAgent(hotelId, employeeId, modObject);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.AGENT_MOD);

    // return output
    return await agentDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delAgent = async (args, req) => {
  const FUNCTION_NAME = "delAgent";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.AGENT, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete mongo data
    const delObject = await mongoDelAgent(hotelId, employeeId, _id);

    // delete redis data
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await redisDelAgent(hotelId, employeeId, delObject._id);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.AGENT_DEL);

    // return output
    return await agentDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const delAgents = async (args, req) => {
  const FUNCTION_NAME = "delAgents";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _ids } = args.agentsInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.AGENT, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_ids) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete mongo data
    const delObjects = await mongoDelAgents(hotelId, employeeId, _ids[0].split(","));

    // delete redis data
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      delObjects.map(async (object) => {
        await redisDelAgent(hotelId, employeeId, object._id);
      });
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.AGENTS_DEL);

    // return output
    return delObjects.map(async (object) => {
      return await agentDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { agents, searchAgent, getAgent, addAgent, modAgent, delAgent, delAgents };