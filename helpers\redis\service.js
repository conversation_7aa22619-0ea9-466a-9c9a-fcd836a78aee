const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");


// Function to get one service data from Redis
const getOneServiceRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_SERVICE}_${hotelId}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all service data from Redis
const getAllServiceRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_SERVICE}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(
                async (hashKey) => {
                    return await readHashValues(hashKey);
                })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set service data in Redis
const setOneServiceRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneServiceRedis(hotelId, data._id);
            await addHashValues(`${process.env.HASH_SERVICE}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    unitPrice: data.unitPrice,
                    description: data.description,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set service data in Redis
const setAllServiceRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllServiceRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_SERVICE}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        unitPrice: data.unitPrice,
                        description: data.description,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                );
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete service data from Redis
const delOneServiceRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_SERVICE}_${hotelId}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete service data from Redis
const delAllServiceRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_SERVICE}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey);
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};


module.exports = {
    getOneServiceRedis, getAllServiceRedis, setOneServiceRedis,
    setAllServiceRedis, delOneServiceRedis, delAllServiceRedis
};