function generateOTP() {
  let otp = "";

  try {
    for (let i = 1; i <= process.env.OTP_LENGTH; i++) {
      let index = Math.floor(Math.random() * (process.env.OTP_DIGITS.length));
      otp = otp + process.env.OTP_DIGITS[index];
    }
  } catch (e) {
    // return otp;
  }

  return otp;
};

function AddMinutesToDate(date, minutes) {
  return new Date(date.getTime() + minutes * 60000);
};


module.exports = { generateOTP, AddMinutesToDate };
