const allowedOptions = require('./allowedOptions');

const corsOptions = {
    origin: (origin, callback) => {
        if (allowedOptions.indexOf(origin) !== -1 || !origin) {
            callback(null, true);
        } else {
            if (allowedOptions.indexOf("*") !== -1) callback(null, true);
            else callback(new Error("Not allowed by CORS..."));
        }
    },
    optionsSuccessStatus: 200
};


module.exports = corsOptions;