const { subDays } = require("date-fns");
const { mongoGetAgent } = require("./mongo/agentMongo");
const { mongoGetPlan } = require("./mongo/planMongo");
const { redisGetAgent } = require("./redis/agentRedis");
const { redisGetPlan } = require("./redis/planRedis");
const { mongoAdvanceBookings, mongoSearchAdvanceBooking, mongoGetAdvanceBooking,
  mongoAddAdvanceBooking, mongoModAdvanceBooking, mongoDelAdvanceBooking
} = require("./mongo/advanceBookingMongo");
const { isAuthorized } = require("../../helpers/authorize");
const { advanceBookingDetail } = require("../../helpers/db");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { stringToDbDate } = require("../../helpers/date");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "advanceBookings.js";


// get all documents of a collection
const advanceBookings = async (args, req) => {
  const FUNCTION_NAME = "advanceBookings";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    const allObjects = await mongoAdvanceBookings(hotelId, employeeId);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ADVANCE_BOOKING_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await advanceBookingDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchAdvanceBooking = async (args, req) => {
  const FUNCTION_NAME = "searchAdvanceBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { startDate, endDate } = args.advanceBookingSearchInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    const startDateInput =
      startDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(startDate)), 1).toISOString();

    const endDateInput =
      endDate == ""
        ? new Date()
        : subDays(new Date(stringToDbDate(endDate)), 1).toISOString();

    // search data
    const searchObjects = await mongoSearchAdvanceBooking(hotelId, employeeId, startDateInput, endDateInput);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ADVANCE_BOOKING_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await advanceBookingDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getAdvanceBooking = async (args, req) => {
  const FUNCTION_NAME = "getAdvanceBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get single data
    const getObject = await mongoGetAdvanceBooking(hotelId, employeeId, _id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ADVANCE_BOOKING_GET);

    // return output
    return await advanceBookingDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addAdvanceBooking = async (args, req) => {
  const FUNCTION_NAME = "addAdvanceBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { guestName, guestMobile, guestEmail, guestCount,
    company, companyAddress, companyGstNo, plan, agent, categories } = args.advanceBookingInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (guestName === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (guestMobile === "") throw new customError(ERR_MSG.INVALID_MOBILE, ERR_CODE.BAD_REQUEST);
    if (guestCount <= 0) throw new customError(ERR_MSG.INVALID_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (plan === "") throw new customError(ERR_MSG.INVALID_PLAN, ERR_CODE.BAD_REQUEST);
    if (agent === "") throw new customError(ERR_MSG.INVALID_AGENT, ERR_CODE.BAD_REQUEST);

    //check if plan exists
    let planObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      planObject = await mongoGetPlan(hotelId, employeeId, plan);
    } else {
      planObject = await redisGetPlan(hotelId, employeeId, plan);
    }
    if (!planObject) throw new customError(ERR_MSG.PLAN_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check if agent exists
    let agentObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      agentObject = await mongoGetAgent(hotelId, employeeId, agent);
    } else {
      agentObject = await redisGetAgent(hotelId, employeeId, agent);
    }
    if (!agentObject) throw new customError(ERR_MSG.AGENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const _name = guestName.trim().toUpperCase();
    const _mobile = guestMobile.trim();
    const _email = guestEmail.trim().toLowerCase();
    const _guestCount = guestCount;
    const _company = company ? company.trim().toUpperCase() : "";
    const _companyAddress = companyAddress ? companyAddress.trim().toUpperCase() : "";
    const _companyGstNo = companyGstNo ? companyGstNo.trim().toUpperCase() : "";
    const _plan = planObject._id;
    const _agent = agentObject._id;

    // add data
    const addObject = await mongoAddAdvanceBooking(hotelId, employeeId, _name, _mobile, _email, _guestCount,
      _company, _companyAddress, _companyGstNo, _plan, _agent, categories);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ADVANCE_BOOKING_ADD);

    // return output
    return await advanceBookingDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modAdvanceBooking = async (args, req) => {
  const FUNCTION_NAME = "modAdvanceBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, guestName, guestMobile, guestEmail, guestCount, company, companyAddress, companyGstNo,
    plan, agent, categories } = args.advanceBookingInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (guestName === "") throw new customError(ERR_MSG.INVALID_NAME, ERR_CODE.BAD_REQUEST);
    if (guestMobile === "") throw new customError(ERR_MSG.INVALID_MOBILE, ERR_CODE.BAD_REQUEST);
    if (guestCount <= 0) throw new customError(ERR_MSG.INVALID_GUEST_COUNT, ERR_CODE.BAD_REQUEST);
    if (plan === "") throw new customError(ERR_MSG.INVALID_PLAN, ERR_CODE.BAD_REQUEST);
    if (agent === "") throw new customError(ERR_MSG.INVALID_AGENT, ERR_CODE.BAD_REQUEST);

    //check if plan exists
    let planObject = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      planObject = await mongoGetPlan(hotelId, employeeId, plan);
    } else {
      planObject = await redisGetPlan(hotelId, employeeId, plan);
    }
    if (!planObject) throw new customError(ERR_MSG.PLAN_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // check if agent exists
    let agentObject = null;

    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      agentObject = await mongoGetAgent(hotelId, employeeId, agent);
    } else {
      agentObject = await redisGetAgent(hotelId, employeeId, agent);
    }
    if (!agentObject) throw new customError(ERR_MSG.AGENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const _name = guestName.trim().toUpperCase();
    const _mobile = guestMobile.trim();
    const _email = guestEmail.trim().toLowerCase();
    const _guestCount = guestCount;
    const _company = company ? company.trim().toUpperCase() : "";
    const _companyAddress = companyAddress ? companyAddress.trim().toUpperCase() : "";
    const _companyGstNo = companyGstNo ? companyGstNo.trim().toUpperCase() : "";
    const _plan = planObject._id;
    const _agent = agentObject._id;

    // modify data
    const modObject = await mongoModAdvanceBooking(hotelId, employeeId, _id, _name, _mobile, _email, _guestCount,
      _company, _companyAddress, _companyGstNo, _plan, _agent, categories);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ADVANCE_BOOKING_MOD);

    // return output
    return await advanceBookingDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delAdvanceBooking = async (args, req) => {
  const FUNCTION_NAME = "delAdvanceBooking";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.BOOKING, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete data
    const delObject = await mongoDelAdvanceBooking(hotelId, employeeId, _id);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.ADVANCE_BOOKING_DEL);

    // return output
    return await advanceBookingDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  advanceBookings, searchAdvanceBooking, getAdvanceBooking, addAdvanceBooking,
  modAdvanceBooking, delAdvanceBooking
};