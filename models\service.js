const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;


const serviceSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    name: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    unitPrice: {
      type: Number,
      required: true
    },
    description: {
      type: String,
      trim: true
    },
    isEnable: {
      type: Boolean,
      default: true,
      required: true
    },
  },
  { timestamps: true }
);


module.exports = new mongoose.model("Service", serviceSchema);