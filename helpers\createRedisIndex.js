const redis = require('./redis/redisClient');
const { Command } = require('ioredis');


// Create Redis Index for collection
const createRedisIndex = async (name) => {
    try {
        switch (name) {
            // GST
            case process.env.INDEX_GST_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_GST}`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'minTariff', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            // Hotel
            case process.env.INDEX_HOTEL_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_HOTEL}`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_HOTEL_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_HOTEL}`,
                    'SCHEMA',
                    'isEnable', 'TAG',
                    'urlName', 'TEXT',
                    'urlPlace', 'TEXT',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            // Role
            case process.env.INDEX_ROLE_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_ROLE}`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_ROLE_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_ROLE}`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            // Employee
            case process.env.INDEX_EMPLOYEE_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_EMPLOYEE}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_EMPLOYEE_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_EMPLOYEE}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            // Plan
            case process.env.INDEX_PLAN_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_PLAN}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_PLAN_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_PLAN}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            // Agent
            case process.env.INDEX_AGENT_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_AGENT}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_AGENT_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_AGENT}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            // case process.env.DB_AGENT_INDEX_VECTOR: {
            //     const keyPrefix = `${process.env.HASH_AGENT}_`;

            //     // Drop existing index if it exists
            //     try {
            //         await redis.sendCommand(new Command('FT.DROPINDEX', [name]));
            //     } catch (error) {
            //         console.log('ℹ️  No existing index to drop');
            //     }

            //     // Create the vector search index
            //     const createCommand = new Command('FT.CREATE', [
            //         name,
            //         'ON', 'HASH',
            //         'PREFIX', '1', keyPrefix,
            //         'SCHEMA',
            //         'embedding', 'VECTOR', 'FLAT', '6',
            //         'TYPE', 'FLOAT32',
            //         'DIM', '768',
            //         'DISTANCE_METRIC', 'COSINE',
            //         'hotel', 'TAG',
            //         'isEnable', 'TAG',
            //         'name', 'TAG',
            //         'description', 'TEXT'
            //     ]);

            //     const result = await redis.sendCommand(createCommand);
            //     break;
            // }

            // Identification
            case process.env.INDEX_ID_CARD_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_IDENTIFICATION}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_ID_CARD_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_IDENTIFICATION}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            // Payment mode
            case process.env.INDEX_PAYMENT_MODE_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_PAYMENT_MODE}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_PAYMENT_MODE_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_PAYMENT_MODE}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            // Room category
            case process.env.INDEX_ROOM_CATEGORY_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_ROOM_CATEGORY}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_ROOM_CATEGORY_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_ROOM_CATEGORY}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'SORTABLE'
                );

                break;
            }

            // Food
            case process.env.INDEX_FOOD_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_FOOD}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'unitPrice', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_FOOD_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_FOOD}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'unitPrice', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            // Service
            case process.env.INDEX_SERVICE_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_SERVICE}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'unitPrice', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_SERVICE_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_SERVICE}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'unitPrice', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            // Miscellaneous
            case process.env.INDEX_MISCELLANEOUS_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_MISCELLANEOUS}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'name', 'TEXT', 'unitPrice', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_MISCELLANEOUS_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_MISCELLANEOUS}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'name', 'TEXT', 'unitPrice', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            // Room
            case process.env.INDEX_ROOM_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_ROOM}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'no', 'TEXT', 'category', 'TEXT', 'accommodation', 'NUMERIC', 'tariff', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_ROOM_HOTEL_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_ROOM}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'no', 'TEXT', 'category', 'TEXT', 'accommodation', 'NUMERIC', 'tariff', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            // Table
            case process.env.INDEX_TABLE_UNIQUE: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_TABLE}_`,
                    'SCHEMA',
                    '_id', 'TAG',
                    'no', 'TEXT', 'accommodation', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            case process.env.INDEX_TABLE_FILTER: {
                await redis.call('FT.CREATE', `${name}`,
                    'ON', 'HASH',
                    'PREFIX', '1', `${process.env.HASH_TABLE}_`,
                    'SCHEMA',
                    'hotel', 'TAG',
                    'isEnable', 'TAG',
                    'no', 'TEXT', 'accommodation', 'NUMERIC', 'SORTABLE'
                );

                break;
            }

            default:
                break;
        }

        return true;
    } catch (error) {
        throw error;
    }
};

// Check if Redis search index exists
const checkRedisIndex = async (name) => {
    try {
        switch (name) {
            // GST
            case process.env.INDEX_GST_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Role
            case process.env.INDEX_ROLE_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_ROLE_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Hotel
            case process.env.INDEX_HOTEL_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_HOTEL_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Employee
            case process.env.INDEX_EMPLOYEE_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_EMPLOYEE_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Plan
            case process.env.INDEX_PLAN_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_PLAN_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Agent
            case process.env.INDEX_AGENT_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_AGENT_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // case process.env.DB_AGENT_INDEX_VECTOR: {
            //     const infoCommand = new Command('FT.INFO', [name]);
            //     const info = await redis.sendCommand(infoCommand);

            //     break;
            // }

            // Identification
            case process.env.INDEX_ID_CARD_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_ID_CARD_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Payment mode
            case process.env.INDEX_PAYMENT_MODE_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_PAYMENT_MODE_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Room category
            case process.env.INDEX_ROOM_CATEGORY_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_ROOM_CATEGORY_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Food
            case process.env.INDEX_FOOD_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_FOOD_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Service
            case process.env.INDEX_SERVICE_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_SERVICE_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Miscellaneous
            case process.env.INDEX_MISCELLANEOUS_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_MISCELLANEOUS_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Room
            case process.env.INDEX_ROOM_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_ROOM_HOTEL_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            // Table
            case process.env.INDEX_TABLE_UNIQUE: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            case process.env.INDEX_TABLE_FILTER: {
                const infoCommand = new Command('FT.INFO', [name]);
                const info = await redis.sendCommand(infoCommand);

                break;
            }

            default: return false;
        }

        return true;
    } catch (error) {
        if (error.message.includes('Unknown Index name')) {
            return false;
        }
        return false;
    }
};

// Drop Redis index
const dropRedisIndex = async (name) => {
    try {
        switch (name) {
            // GST
            case process.env.INDEX_GST_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Role
            case process.env.INDEX_ROLE_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_ROLE_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Hotel
            case process.env.INDEX_HOTEL_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_HOTEL_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Plan
            case process.env.INDEX_PLAN_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_PLAN_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Employee
            case process.env.INDEX_EMPLOYEE_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_EMPLOYEE_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Agent
            case process.env.INDEX_AGENT_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_AGENT_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // case process.env.DB_AGENT_INDEX_VECTOR: {
            //     const dropCommand = new Command('FT.DROPINDEX', [name]);

            //     await redis.sendCommand(dropCommand);
            //     return true;
            // }

            // Identification
            case process.env.INDEX_ID_CARD_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_ID_CARD_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Payment mode
            case process.env.INDEX_PAYMENT_MODE_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_PAYMENT_MODE_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Room category
            case process.env.INDEX_ROOM_CATEGORY_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_ROOM_CATEGORY_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Food
            case process.env.INDEX_FOOD_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_FOOD_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Service
            case process.env.INDEX_SERVICE_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_SERVICE_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Miscellaneous
            case process.env.INDEX_MISCELLANEOUS_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_MISCELLANEOUS_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Room
            case process.env.INDEX_ROOM_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_ROOM_HOTEL_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            // Table
            case process.env.INDEX_TABLE_UNIQUE: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            case process.env.INDEX_TABLE_FILTER: {
                const dropCommand = new Command('FT.DROPINDEX', [name]);

                await redis.sendCommand(dropCommand);
                return true;
            }

            default:
                break;
        }

        return false;
    } catch (error) {
        return false;
    }
};

// Test Redis search index with a simple query
const testRedisIndex = async (name) => {
    try {
        switch (name) {
            // GST
            case process.env.INDEX_GST_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Role
            case process.env.INDEX_ROLE_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_ROLE_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Hotel
            case process.env.INDEX_HOTEL_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_HOTEL_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Plan
            case process.env.INDEX_PLAN_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_PLAN_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Employee
            case process.env.INDEX_EMPLOYEE_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_EMPLOYEE_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Agent
            case process.env.INDEX_AGENT_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_AGENT_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Identification
            case process.env.INDEX_ID_CARED_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_ID_CARD_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Payment mode
            case process.env.INDEX_PAYMENT_MODE_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_PAYMENT_MODE_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Room category
            case process.env.INDEX_ROOM_CATEGORY_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_ROOM_CATEGORY_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Food
            case process.env.INDEX_FOOD_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_FOOD_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Service
            case process.env.INDEX_SERVICE_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_SERVICE_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Miscellaneous
            case process.env.INDEX_MISCELLANEOUS_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_MISCELLANEOUS_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Room
            case process.env.INDEX_ROOM_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_ROOM_HOTEL_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            // Table
            case process.env.INDEX_TABLE_UNIQUE: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            case process.env.INDEX_TABLE_FILTER: {
                const searchCommand = new Command('FT.SEARCH', [
                    name,
                    '*',
                    'LIMIT', '0', '1'
                ]);

                const results = await redis.sendCommand(searchCommand);

                break;
            }

            default:
                break;
        }

        return true;
    } catch (error) {
        return false;
    }
};

// Initialize Redis index (check if exists, create if not)
const initRedisIndex = async () => {
    try {
        // GST
        try {
            const index = process.env.INDEX_GST_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Role
        try {
            const index = process.env.INDEX_ROLE_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_ROLE_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Hotel
        try {
            const index = process.env.INDEX_HOTEL_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_HOTEL_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Employee
        try {
            const index = process.env.INDEX_EMPLOYEE_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_EMPLOYEE_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Plan
        try {
            const index = process.env.INDEX_PLAN_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_PLAN_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Agent
        try {
            const index = process.env.INDEX_AGENT_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_AGENT_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // try {
        //     const index = process.env.DB_AGENT_INDEX_VECTOR;
        //     const exists = await checkRedisIndex(index);

        //     if (!exists) {
        //         await createRedisIndex(index);
        //         await testRedisIndex(index);
        //     }
        // } catch (error) {
        //     return false;
        // }

        // Identification
        try {
            const index = process.env.INDEX_ID_CARD_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_ID_CARD_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Payment mode
        try {
            const index = process.env.INDEX_PAYMENT_MODE_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_PAYMENT_MODE_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Room category
        try {
            const index = process.env.INDEX_ROOM_CATEGORY_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_ROOM_CATEGORY_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Food
        try {
            const index = process.env.INDEX_FOOD_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_FOOD_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Service
        try {
            const index = process.env.INDEX_SERVICE_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_SERVICE_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Miscellaneous
        try {
            const index = process.env.INDEX_MISCELLANEOUS_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_MISCELLANEOUS_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Room
        try {
            const index = process.env.INDEX_ROOM_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_ROOM_HOTEL_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        // Table
        try {
            const index = process.env.INDEX_TABLE_UNIQUE;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        try {
            const index = process.env.INDEX_TABLE_FILTER;
            const exists = await checkRedisIndex(index);

            if (!exists) {
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
            else {
                await dropRedisIndex(index);
                await createRedisIndex(index);
                await testRedisIndex(index);
            }
        } catch (error) {
            return false;
        }

        return true;
    } catch (error) {
        return false;
    }
};


module.exports = { createRedisIndex, checkRedisIndex, dropRedisIndex, testRedisIndex, initRedisIndex };
