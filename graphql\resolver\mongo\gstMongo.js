const GST = require("../../../models/gst");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "gstMongo.js";


// get all documents of a collection
const mongoGSTs = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoGSTS";

  try {
    const order = { maxTariff: 1, minTariff: 1 };
    const cursor = await GST.find({}).sort(order);
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString() };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchGST = async (hotelId, employeeId, searchTariff) => {
  const FUNCTION_NAME = "mongoSearchGST";
  const order = { maxTariff: 1, minTariff: 1 };
  let condition = null;

  try {
    if (!searchTariff) {
      condition = {};
    } else {
      // condition = { maxTariff: { $lte: maxTariff }, minTariff: { $gte: minTariff } };
      condition = { minTariff: { $lte: searchTariff }, maxTariff: { $gte: searchTariff } };
    }

    const cursor = await GST.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString() };
    });

    return spread
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetGST = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetGST";
  let spread = null;

  try {
    // read single data
    const condition = { _id: id };
    const cursor = await GST.findOne(condition);
    if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddGST = async (hotelId, employeeId, minTariff, maxTariff, cGSTPercentage, sGSTPercentage) => {
  const FUNCTION_NAME = "mongoAddGST";

  try {
    // check for duplicate data in db
    const condition = { minTariff: minTariff, maxTariff: maxTariff };
    let duplicateCursor = await GST.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.GST_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // insert data in db
    const data = {
      minTariff: minTariff,
      maxTariff: maxTariff,
      cGSTPercentage: cGSTPercentage,
      sGSTPercentage: sGSTPercentage
    };
    const addData = new GST(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.GST_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { _id: addObject.id };
    const findCursor = await GST.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString() };

    return spread
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModGST = async (hotelId, employeeId, id, maxTariff, minTariff, cGSTPercentage, sGSTPercentage) => {
  const FUNCTION_NAME = "mongoModGST";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, maxTariff: { $eq: maxTariff }, minTariff: { $eq: minTariff } };
    const duplicateCursor = await GST.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.GST_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = {
      maxTariff: maxTariff,
      minTariff: minTariff,
      cGSTPercentage: cGSTPercentage,
      sGSTPercentage: sGSTPercentage
    };
    const modObject = await GST.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.GST_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { _id: modObject.id };
    const findCursor = await GST.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelGST = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelGST";

  try {
    // check for existence
    const condition = { _id: id };
    const cursor = await GST.findOne(condition);
    const spread = { ...cursor._doc, _id: cursor.id.toString() };
    if (!spread) throw new customError(ERR_MSG.GST_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // delete from db
    const delObject = await GST.deleteOne(condition);
    if (!delObject) throw new customError(ERR_MSG.GST_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { mongoGSTs, mongoSearchGST, mongoGetGST, mongoAddGST, mongoModGST, mongoDelGST };