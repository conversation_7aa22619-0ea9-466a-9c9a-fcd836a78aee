const nodemailer = require("nodemailer");


const EMAIL_SETTINGS = {
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false,
    auth: {
        user: process.env.EMAIL_ADDRESS,
        pass: process.env.EMAIL_APP_PASSWORD
    }
};

async function sendOtpEmail(to, otp) {
    try {
        let transporter = nodemailer.createTransport(EMAIL_SETTINGS);

        // send mail with defined transport object
        let info = await transporter.sendMail({
            from: process.env.EMAIL_ADDRESS,
            to: to,
            subject: `HotelApp OTP is ${otp}`,
            html: `<div
                  class="container"
                  style="max-width: 90%; margin: auto; padding-top: 20px">
                  <h2>Welcome to the HotelApp.</h2>
                  <h4>You are officially In ✔</h4>
                  <p style="margin-bottom: 30px;">Pleas enter the sign up OTP to get started</p>
                  <h1 style="font-size: 40px; letter-spacing: 2px; text-align:center;">${otp}</h1>
              </div>`
        });

        return true;
    } catch (e) {
        // console.error(e);
        return false;
    }
};


module.exports = sendOtpEmail;