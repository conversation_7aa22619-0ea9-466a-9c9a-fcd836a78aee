const Hotel = require("../../../models/hotel");
const Room = require("../../../models/room");
const Booking = require("../../../models/booking");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require("../../../helpers/customError");
const { ROOM_STATUS } = require("../../../configs/roomOptions");
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "checkoutMongo.js";


// insert a document into the collection
const mongoAddCheckout = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoAddCheckout";

  try {
    // find booking
    const bookingCondition = { _id: id, hotel: hotelId };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    if (bookingCursor.dueAmount.toFixed(2) > 0) throw new customError(ERR_MSG.INVALID_CHECKOUT, ERR_CODE.BAD_REQUEST);

    // find hotel
    const hotelCondition = { _id: hotelId, isEnable: true };
    const hotelCursor = await Hotel.findOne(hotelCondition);
    const billNo = parseInt(hotelCursor.lastFinalBillNo) + 1;

    // update last food bill no in hotel document
    await Hotel.findByIdAndUpdate(hotelId, { lastFinalBillNo: billNo });

    // update room
    bookingCursor.rooms.map(async (room) => {
      await Room.findByIdAndUpdate(room.room, { status: ROOM_STATUS.EMPTY });
    });

    // update booking
    await Booking.findByIdAndUpdate(id, {
      billNo: billNo,
      status: ROOM_STATUS.CHECKED_OUT,
      updatedAt: new Date()
    });

    // find booking
    const cursor = await Booking.findOne(bookingCondition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      guest: cursor.guest.toString(),
      plan: cursor.plan.toString(),
      agent: cursor.agent.toString(),
      rooms: cursor.rooms.map((room) => {
        return {
          ...room._doc,
          _id: room.id,
          room: room.room.toString()
        };
      })
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { mongoAddCheckout };