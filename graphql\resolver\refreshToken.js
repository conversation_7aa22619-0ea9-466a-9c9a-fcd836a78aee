const jwt = require("jsonwebtoken");
const Employee = require("../../models/employee");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "refreshToken.js";


const refreshToken = async (args, req) => {
  const FUNCTION_NAME = "refreshToken";
  const { hotelId, employeeId } = req;

  let dcoded = null;

  try {
    const { status } = args.refreshTokenInput;

    if (!status) throw new customError(ERR_MSG.INVALID_REQUEST, ERR_CODE.BAD_REQUEST);
    if (status.trim().toUpperCase() !== 'Y') throw new customError(ERR_MSG.INVALID_REQUEST, ERR_CODE.BAD_REQUEST);

    const authHeader = req.headers.authorization || req.headers.Authorization;
    if (!authHeader) return next();

    if (!authHeader?.startsWith("Bearer ")) return next();

    const token = authHeader.split(" ")[1];

    jwt.verify(
      token,
      process.env.JWT_REFRESH_TOKEN_SECRET,
      (error, decoded) => {
        if (error) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);

        dcoded = decoded;
      }
    );

    let findEmployee = await Employee.findOne({
      hotel: dcoded.UserInfo.hotelId,
      name: dcoded.UserInfo.employeeName,
      isEnable: true
    });

    if (!findEmployee) throw new customError(ERR_MSG.EMPLOYEE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    const accessToken = jwt.sign(
      {
        UserInfo: {
          hotelId: findEmployee.hotel,
          employeeId: findEmployee._id,
          employeeName: findEmployee.name,
          employeeRole: findEmployee.role
        }
      },
      process.env.JWT_TOKEN_SECRET,
      { expiresIn: process.env.JWT_TOKEN_EXPIRES }
    );

    const refreshToken = jwt.sign(
      {
        UserInfo: {
          hotelId: findEmployee.hotel,
          employeeName: findEmployee.name
        }
      },
      process.env.JWT_REFRESH_TOKEN_SECRET,
      { expiresIn: process.env.JWT_REFRESH_TOKEN_EXPIRES }
    );

    jwt.verify(
      accessToken,
      process.env.JWT_TOKEN_SECRET,
      async (error, decoded) => {
        if (error) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);

        findEmployee.otp = "";
        findEmployee.expirationTime = null;
        findEmployee.iat = decoded.iat;

        await findEmployee.save();
      }
    );

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.REFRESH_TOKEN);

    return { accessToken, refreshToken };
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { refreshToken };