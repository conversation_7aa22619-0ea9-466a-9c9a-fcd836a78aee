const authResolver = require("./auth");
const refreshTokenResolver = require("./refreshToken");
const roleResolver = require("./roles");
const paymentModeResolver = require("./paymentModes");
const agentResolver = require("./agents");
const identificationResolver = require("./identifications");
const gstResolver = require("./gsts");
const hotelResolver = require("./hotels");
const employeeResolver = require("./employees");
const planResolver = require("./plans");
const foodResolver = require("./foods");
const serviceResolver = require("./services");
const miscellaneousResolver = require("./miscellanea");
const roomCategoryResolver = require("./roomCategories");
const roomResolver = require("./rooms");
const tableResolver = require("./tables");
const guestResolver = require("./guests");
const advanceBookingResolver = require("./advanceBookings");
const bookingResolver = require("./bookings");
const oldBookingResolver = require("./oldBookings");
const orderResolver = require("./orders");
const breakfastResolver = require("./breakfast");
const billResolver = require("./bills");
const ledgerResolver = require("./ledger");
const paymentResolver = require("./payments");
const checkOutResolver = require("./checkout");
const yearEndResolver = require("./yearEnd");
const activityLogResolver = require("./activityLog");
const errorLogResolver = require("./errorLog");
const dbOperations = require("./dbOperations");

const rootResolver = {
  ...authResolver,
  ...refreshTokenResolver,
  ...roleResolver,
  ...paymentModeResolver,
  ...agentResolver,
  ...identificationResolver,
  ...gstResolver,
  ...hotelResolver,
  ...employeeResolver,
  ...planResolver,
  ...foodResolver,
  ...serviceResolver,
  ...miscellaneousResolver,
  ...roomCategoryResolver,
  ...roomResolver,
  ...tableResolver,
  ...guestResolver,
  ...advanceBookingResolver,
  ...bookingResolver,
  ...oldBookingResolver,
  ...orderResolver,
  ...breakfastResolver,
  ...billResolver,
  ...ledgerResolver,
  ...paymentResolver,
  ...checkOutResolver,
  ...yearEndResolver,
  ...activityLogResolver,
  ...errorLogResolver,
  ...dbOperations
};

module.exports = rootResolver;