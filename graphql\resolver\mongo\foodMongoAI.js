const mongoose = require("mongoose");
const Food = require("../../../models/food");
const { processTextToEmbedding } = require("../../../helpers/embedding");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "foodMongoAI.js";


// search within collection
const mongoAISearchFood = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoAISearchFood";

  try {
    if (searchKey === "") {
      // read all documents from db
      const condition = { hotel: hotelId, isEnable: true };
      const order = { name: 1 };
      const cursor = await Food.find(condition).sort(order);

      // spread data
      const spread = cursor.map((object) => {
        return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() }
      });

      return spread;
    }
    else {
      // get data
      // const searchEmbedding = await createEmbedding(searchKey);
      const searchEmbedding = await processTextToEmbedding(searchKey);

      const pipeline = [
        {
          $vectorSearch: {
            index: process.env.DB_FOOD_INDEX_VECTOR,
            path: 'embedding',
            queryVector: searchEmbedding,
            numCandidates: 10,
            limit: 10,
            filter: {
              $and: [
                { hotel: { $eq: new mongoose.Types.ObjectId(hotelId) } },
                { isEnable: { $eq: true } }
              ]
            }
          }
        },
        {
          $project: {
            _id: 1,
            hotel: 1,
            name: 1,
            unitPrice: 1,
            description: 1,
            score: { $meta: 'vectorSearchScore' }
          }
        }
      ];
      const cursor = await Food.aggregate(pipeline).exec();

      // spread data
      const spread = cursor
        .filter((object) => object.score >= 0.65)
        .map((object) => {
          return {
            _id: object._id.toString(),
            hotel: object.hotel.toString(),
            name: object.name,
            unitPrice: object.unitPrice,
            description: object.description
          };
        });

      return spread;
    }
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAIAddFood = async (hotelId, employeeId, name, unitPrice, description) => {
  const FUNCTION_NAME = "mongoAIAddFood";

  try {
    // check for duplicate data in db
    const condition = { hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Food.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.FOOD_CONFLICT, ERR_CODE.CONFLICT);

    // add data in vector db
    const summary = `'${name}' is a delicious food item. It costs ${unitPrice} rupees per plate. ${description ? ` ${description}.` : ''}`;
    // const embedding = await createEmbedding(summary);
    const embedding = await processTextToEmbedding(summary);

    // insert data in db
    const data = {
      hotel: hotelId,
      name: name,
      unitPrice: unitPrice,
      description: description,
      embedding: embedding
    };
    const addData = new Food(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.FOOD_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: addObject.id };
    const findCursor = await Food.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoAIModFood = async (hotelId, employeeId, id, name, unitPrice, description) => {
  const FUNCTION_NAME = "mongoAIModFood";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Food.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.FOOD_CONFLICT, ERR_CODE.CONFLICT);

    // mod data in vector db
    const summary = `'${name}' is a food item. It costs ${unitPrice} rupees per plate. ${description ? ` ${description}.` : ''}`;
    // const embedding = await createEmbedding(summary);
    const embedding = await processTextToEmbedding(summary);

    // change data in db
    const modData = {
      name: name,
      unitPrice: unitPrice,
      description: description,
      embedding: embedding
    };
    const modObject = await Food.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.FOOD_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { hotel: hotelId, _id: modObject.id };
    const findCursor = await Food.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { mongoAISearchFood, mongoAIAddFood, mongoAIModFood };