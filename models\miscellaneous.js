const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;


const miscellaneousSchema = new Schema(
  {
    hotel: {
      type: String,
      required: true,
      type: Schema.Types.ObjectId
    },
    name: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    unitPrice: {
      type: Number,
      required: true,
      min: 0.01
    },
    description: {
      type: String,
      trim: true
    },
    isEnable: {
      type: Boolean,
      default: true,
      required: true
    },
    embedding: {
      type: [Number],
      default: []
    }
  },
  { timestamps: true }
);


module.exports = new mongoose.model("Miscellaneous", miscellaneousSchema);