const bcrypt = require("bcryptjs");
const Employee = require("../../../models/employee");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "employeeMongo.js";


// get all documents of a collection
const mongoEmployees = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoEmployees";

  try {
    // read all document from db
    const condition = { hotel: hotelId, isEnable: true };
    const order = { name: 1, mobile: 1, email: 1 };
    const cursor = await Employee.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id.toString(),
        hotel: object.hotel.toString(),
        role: object.role.toString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchEmployee = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoSearchEmployee";
  const order = { name: 1 };
  let condition = null;

  try {
    if (!searchKey.trim()) {
      condition = { hotel: hotelId, isEnable: true };
    }
    else {
      condition = { hotel: hotelId, isEnable: true, $text: { $search: searchKey } };
    }

    const cursor = await Employee.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id.toString(),
        hotel: object.hotel.toString(),
        role: object.role.toString()
      }
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetEmployee = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetEmployee";
  let spread = null;

  try {
    // read single data
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Employee.findOne(condition);
    if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString(), hotel: cursor.hotel.toString(), role: cursor.role.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddEmployee = async (hotelId, employeeId, role, name, address, mobile, email) => {
  const FUNCTION_NAME = "mongoAddEmployee";

  try {
    // check for duplicate data in db
    const condition = { hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Employee.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.EMPLOYEE_CONFLICT, ERR_CODE.CONFLICT);

    const hashedPassword = await bcrypt.hash(mobile, 12);

    // insert data in db
    const data = {
      hotel: hotelId, role: role, name: name, address: address, mobile: mobile,
      email: email, password: hashedPassword
    };
    const addData = new Employee(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.EMPLOYEE_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: addObject.id };
    const findCursor = await Employee.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString(), role: findCursor.role.toString() };

    return spread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModEmployee = async (hotelId, employeeId, id, role, name, address, mobile, email) => {
  const FUNCTION_NAME = "mongoModEmployee";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Employee.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.EMPLOYEE_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = { role: role, name: name, address: address, mobile: mobile, email: email };
    const modObject = await Employee.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.EMPLOYEE_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { hotel: hotelId, _id: modObject.id };
    const findCursor = await Employee.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString(), role: findCursor.role.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelEmployee = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelEmployee";

  try {
    // check for existence
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Employee.findOne(condition);
    const spread = { ...cursor._doc, _id: cursor.id.toString(), hotel: cursor.hotel.toString(), role: cursor.role.toString() };
    if (!spread) throw new customError(ERR_MSG.EMPLOYEE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // delete from db
    const delObject = await Employee.findByIdAndUpdate(id, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.EMPLOYEE_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const mongoDelEmployees = async (hotelId, employeeId, ids) => {
  const FUNCTION_NAME = "mongoDelEmployees";

  try {
    // read all agents from db
    const condition = { _id: { $in: ids }, hotel: hotelId, isEnable: true };
    const cursor = await Employee.find(condition);
    if (cursor.length !== ids.length) throw new customError(ERR_MSG.EMPLOYEE_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString(), role: object.role.toString() };
    });

    // delete from db
    const delArray = await Employee.updateMany({ _id: { $in: ids } }, { isEnable: false });
    if (!delArray) throw new customError(ERR_MSG.EMPLOYEE_NOT_DELETE, ERR_CODE.INTERNAL);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  mongoEmployees, mongoSearchEmployee, mongoGetEmployee, mongoAddEmployee,
  mongoModEmployee, mongoDelEmployee, mongoDelEmployees
};