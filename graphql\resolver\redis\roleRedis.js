const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneRoleRedis } = require("../../../helpers/redis/role");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "roleRedis.js";


// get all documents of a collection
const redisRoles = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisRoles";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROLE_FILTER}`,
      query,
      'RETURN', '6', '_id', 'hotel', 'name', 'color', 'permissions', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '20'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      // spread data with populated hotel
      const spread = results.map((object) => {
        return {
          _id: object._id,
          hotel: object.hotel,
          name: object.name,
          color: object.color,
          permissions: JSON.parse(object.permissions),
          description: object.description
        };
      });

      return spread
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchRole = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchRole";
  const regex = new RegExp(searchKey, "i");

  let filter = null;

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROLE_FILTER}`,
      query,
      'RETURN', '6', '_id', 'hotel', 'name', 'color', 'permissions', 'description',
      'SORTBY', 'name', 'ASC',
      'LIMIT', '0', '20'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        filter = results;
      }
      else {
        // filter array
        filter = results.filter((item) => regex.test(item.name) ||
          regex.test(item.description));
      }

      // spread data with populated hotel
      const spread = filter.map((object) => {
        return {
          _id: object._id,
          hotel: object.hotel,
          name: object.name,
          color: object.color,
          permissions: JSON.parse(object.permissions),
          description: object.description
        };
      });

      return spread
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetRole = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetRole";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROLE_UNIQUE}`,
      query,
      'RETURN', '6', '_id', 'hotel', 'name', 'color', 'permissions', 'description',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      const result = results[0];

      // Populate hotel data
      return {
        _id: result._id,
        hotel: result.hotel,
        name: result.name,
        color: result.color,
        permissions: JSON.parse(result.permissions),
        description: result.description
      };
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetRole = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetRole";

  try {
    // read single data
    await setOneRoleRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelRole = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelRole";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_ROLE}_${hotelId}:${id}`);

    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { redisRoles, redisSearchRole, redisGetRole, redisSetRole, redisDelRole };