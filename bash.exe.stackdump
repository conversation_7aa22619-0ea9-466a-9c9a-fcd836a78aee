Stack trace:
Frame         Function      Args
0007FFFF9F20  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8E20) msys-2.0.dll+0x1FE8E
0007FFFF9F20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA1F8) msys-2.0.dll+0x67F9
0007FFFF9F20  000210046832 (000210286019, 0007FFFF9DD8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9F20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9F20  000210068E24 (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA200  00021006A225 (0007FFFF9F30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFED1780000 ntdll.dll
7FFED0E10000 KERNEL32.DLL
7FFECEAF0000 KERNELBASE.dll
7FFED1330000 USER32.dll
000210040000 msys-2.0.dll
7FFECF360000 win32u.dll
7FFED0EE0000 GDI32.dll
7FFECF190000 gdi32full.dll
7FFECEEF0000 msvcp_win.dll
7FFECF040000 ucrtbase.dll
7FFED1060000 advapi32.dll
7FFED1500000 msvcrt.dll
7FFECF7E0000 sechost.dll
7FFED0100000 RPCRT4.dll
7FFECDDA0000 CRYPTBASE.DLL
7FFECEFA0000 bcryptPrimitives.dll
7FFED1120000 IMM32.DLL
