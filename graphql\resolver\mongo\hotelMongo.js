const Hotel = require("../../../models/hotel");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "hotelMongo.js";


// get all documents of a collection
const mongoHotels = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoHotels";

  try {
    // read all document from db
    const condition = { isEnable: true };
    const order = { name: 1 };
    const cursor = await Hotel.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString() };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchHotel = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoSearchHotel";
  const order = { name: 1 };
  let condition = null;

  try {
    if (!searchKey.trim()) {
      condition = { isEnable: true };
    }
    else {
      condition = { isEnable: true, $text: { $search: searchKey } };
    }

    const cursor = await Hotel.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString() }
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetHotel = async (id) => {
  let spread = null;

  try {
    // read single data
    const condition = { _id: id, isEnable: true };
    const cursor = await Hotel.findOne(condition);
    if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString() };

    return spread;
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

// find a document by place & name from a collection
const mongoGetHotelFromUrl = async (urlName, urlPlace) => {
  let spread = null;

  try {
    const condition = { urlName: urlName, urlPlace: urlPlace, isEnable: true };
    const cursor = await Hotel.findOne(condition);
    if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString() };

    return spread;
  } catch (error) {
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddHotel = async (hotelId, employeeId, name, address, city, state, pin,
  phone, email, gstNo, urlName, urlPlace) => {
  const FUNCTION_NAME = "mongoAddHotel";

  try {
    // check for duplicate data in db
    const condition = { name: name, isEnable: true };
    const duplicateCursor = await Hotel.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.HOTEL_CONFLICT, ERR_CODE.CONFLICT);

    // insert data in db
    const data = {
      hotel: hotelId, name: name, address: address, city: city, state: state,
      pin: pin, phone: phone, email: email, gstNo: gstNo, urlName: urlName, urlPlace: urlPlace
    };
    const addData = new Hotel(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.HOTEL_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { _id: addObject.id };
    const findCursor = await Hotel.findById(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString() };

    return spread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModHotel = async (hotelId, employeeId, id, name, address, city, state, pin,
  phone, email, gstNo, urlName, urlPlace) => {
  const FUNCTION_NAME = "mongoModHotel";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, name: name, isEnable: true };
    const duplicateCursor = await Hotel.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.HOTEL_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = {
      name: name, address: address, city: city, state: state,
      pin: pin, phone: phone, email: email, gstNo: gstNo, urlName: urlName, urlPlace: urlPlace
    };
    const modObject = await Hotel.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.HOTEL_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { _id: modObject.id };
    const findCursor = await Hotel.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString() };

    return spread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelHotel = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelHotel";

  try {
    // check for existence
    const condition = { _id: id, isEnable: true };
    const cursor = await Hotel.findOne(condition);
    const spread = { ...cursor._doc, _id: cursor.id.toString() };
    if (!spread) throw new customError(ERR_MSG.HOTEL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // delete from db
    const delObject = await Hotel.findByIdAndUpdate(id, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.HOTEL_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const mongoDelHotels = async (hotelId, employeeId, ids) => {
  const FUNCTION_NAME = "mongoDelHotels";

  try {
    // read all agents from db
    const condition = { _id: { $in: ids }, isEnable: true };
    const cursor = await Hotel.find(condition);
    if (cursor.length !== ids.length) throw new customError(ERR_MSG.HOTEL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString() };
    });

    // delete from db
    const delArray = await Hotel.updateMany({ _id: { $in: ids } }, { isEnable: false });
    if (!delArray) throw new customError(ERR_MSG.HOTEL_NOT_DELETE, ERR_CODE.INTERNAL);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  mongoHotels, mongoSearchHotel, mongoGetHotel, mongoGetHotelFromUrl,
  mongoAddHotel, mongoModHotel, mongoDelHotel, mongoDelHotels
};