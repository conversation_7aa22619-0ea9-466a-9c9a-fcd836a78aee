const { min } = require("moment");
const mongoose = require("mongoose");
const Schema = mongoose.Schema;


const guestSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    employee: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Employee"
    },
    name: {
      type: String,
      required: true,
      trim: true,
      uppercase: true
    },
    address: {
      type: String,
      trim: true,
      uppercase: true
    },
    city: {
      type: String,
      trim: true,
      uppercase: true
    },
    policeStation: {
      type: String,
      trim: true,
      uppercase: true
    },
    state: {
      type: String,
      trim: true,
      uppercase: true
    },
    pin: {
      type: String,
      trim: true
    },
    mobile: {
      type: String
    },
    email: {
      type: String,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        "Invalid email!"
      ]
    },
    father: {
      type: String,
      trim: true,
      uppercase: true
    },
    age: {
      type: Number,
    },
    guestCount: {
      type: Number,
      required: true,
      min: 1,
      max: 100
    },
    maleCount: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    femaleCount: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    childCount: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    },
    identification: {
      type: Schema.Types.ObjectId,
      ref: "Identification"
    },
    idNo: {
      type: String,
      trim: true,
      uppercase: true
    },
    company: {
      type: String,
      trim: true,
      uppercase: true
    },
    companyAddress: {
      type: String,
      trim: true,
      uppercase: true
    },
    companyGstNo: {
      type: String,
      trim: true,
      uppercase: true
    },
    isEnable: {
      type: Boolean,
      default: true,
      required: true
    }
  },
  { timestamps: true }
);


module.exports = mongoose.model("Guest", guestSchema);