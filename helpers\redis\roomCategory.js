const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");


// Function to get one room category data from Redis
const getOneRoomCategoryRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_ROOM_CATEGORY}_${hotelId}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all room category data from Redis
const getAllRoomCategoryRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_ROOM_CATEGORY}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set room category data in Redis
const setOneRoomCategoryRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneRoomCategoryRedis(hotelId, data._id);

            await addHashValues(`${process.env.HASH_ROOM_CATEGORY}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    accommodation: data.accommodation,
                    tariff: data.tariff,
                    extraBedTariff: data.extraBedTariff,
                    extraPersonTariff: data.extraPersonTariff,
                    maxDiscount: data.maxDiscount,
                    description: data.description,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set room category data in Redis
const setAllRoomCategoryRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllRoomCategoryRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_ROOM_CATEGORY}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        accommodation: data.accommodation,
                        tariff: data.tariff,
                        extraBedTariff: data.extraBedTariff,
                        extraPersonTariff: data.extraPersonTariff,
                        maxDiscount: data.maxDiscount,
                        description: data.description,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                )
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete agent data from Redis
const delOneRoomCategoryRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_ROOM_CATEGORY}_${hotelId}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete room category data from Redis
const delAllRoomCategoryRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_ROOM_CATEGORY}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey);
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};


module.exports = {
    getOneRoomCategoryRedis, getAllRoomCategoryRedis, setOneRoomCategoryRedis,
    setAllRoomCategoryRedis, delOneRoomCategoryRedis, delAllRoomCategoryRedis
};
