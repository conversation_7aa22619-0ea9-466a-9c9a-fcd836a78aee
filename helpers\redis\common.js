const redis = require("./redisClient");


const decodeRedisVectorResults = (raw) => {
  const decoder = new TextDecoder();
  const results = [];

  for (let i = 1; i < raw.length; i += 2) {
    const key = decoder.decode(raw[i]);
    const fields = raw[i + 1];
    const doc = { _key: key };

    for (let j = 0; j < fields.length; j += 2) {
      const field = decoder.decode(fields[j]);
      const value = decoder.decode(fields[j + 1]);
      doc[field] = value;
    }

    results.push(doc);
  }

  return results;
}

const searchHashKeys = async (prefix, pattern) => {
  let cursor = 0;
  let keys = [];
  const scanCount = process.env.DB_REDIS_SCAN_COUNT || 500;

  try {
    do {
      const [nextCursor, foundKeys] = await redis.scan(cursor, ['MATCH', `${prefix}:${pattern}`, 'COUNT', scanCount]);
      cursor = nextCursor;
      keys.push(...foundKeys);
    } while (cursor !== '0');

    return keys;
  } catch (err) {
    console.error(err.message);
  }
};

const readHashValues = async (hashKey) => {
  return new Promise((resolve, reject) => {
    redis.hgetall(hashKey, (err, result) => {
      if (err) reject(err);
      else resolve(result);
    });
  });
};

const addHashValues = async (hashKey, value) => {
  try {
    await redis.hset(hashKey, value);
    await redis.expire(hashKey, process.env.DB_REDIS_EXPIRY_TIME);
  } catch (err) {
    console.error(err.message);
  }
};

const delHashKey = (hashKey) => {
  try {
    const data = redis.del(hashKey);
    return data;
  } catch (err) {
    console.error(err.message);
  }
};


module.exports = { searchHashKeys, readHashValues, addHashValues, delHashKey, decodeRedisVectorResults };