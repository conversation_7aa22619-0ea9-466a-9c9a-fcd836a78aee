const mongoose = require("mongoose");
const { subDays, addDays } = require("date-fns");
const Room = require("../../../models/room");
const Guest = require("../../../models/guest");
const Booking = require("../../../models/booking");
const { GSTPercentageFind } = require("../../../helpers/db");
const { stringToDbDate } = require("../../../helpers/date");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ROOM_STATUS } = require("../../../configs/roomOptions");
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "bookingMongo.js";


// Get all documents of a collection
const mongoBookings = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoBookings";

  try {
    // read all document from db
    const condition = { hotel: hotelId, status: { $nin: [ROOM_STATUS.ADVANCE_BOOKED, ROOM_STATUS.CHECKED_OUT] } };
    const order = { startDate: 1 };
    const cursor = await Booking.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString(),
        roomNos: object.roomNos,
        rooms: object.rooms.map((room) => {
          return {
            ...room._doc,
            _id: room.id,
            room: room.room.toString()
          };
        }),
        startDate: object.startDate.toISOString(),
        endDate: object.endDate.toISOString(),
        billNo: object.billNo ? object.billNo : "",
        dueAmount: object.dueAmount,
        status: object.status,
        createdAt: object.createdAt.toISOString(),
        updatedAt: object.updatedAt.toISOString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchBooking = async (hotelId, employeeId, startDate, endDate) => {
  const FUNCTION_NAME = "mongoSearchBooking";

  try {
    const condition = {
      hotel: hotelId,
      status: { $nin: [ROOM_STATUS.CHECKED_OUT, ROOM_STATUS.ADVANCE_BOOKED] },
      startDate: { $gte: new Date(startDate), $lte: new Date(endDate) }
    };
    const order = { billNo: 1, startDate: 1 };
    const cursor = await Booking.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString(),
        rooms: object.rooms.map((room) => {
          return {
            ...room._doc,
            _id: room.id,
            room: room.room.toString()
          };
        })
      };
    });

    return spread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetBooking = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetBooking";
  let spread = null;

  try {
    // read single data
    const condition = { _id: id, hotel: hotelId };
    const cursor = await Booking.findOne(condition);
    if (cursor) spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employees: cursor.employees.map((employee) => employee.toString()),
      guest: cursor.guest.toString(),
      plan: cursor.plan.toString(),
      agent: cursor.agent.toString(),
      rooms: cursor.rooms.map((room) => {
        return {
          ...room._doc,
          _id: room.id,
          room: room.room.toString()
        };
      })
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddBooking = async (hotelId, employeeId, guestId, planId, agentId, rooms) => {
  const FUNCTION_NAME = "mongoAddBooking";

  try {
    let occupancyDates = [];
    for (const object of rooms) {
      occupancyDates.push(subDays(new Date(stringToDbDate(object.occupancyDate)), 1));
    }
    const startDate = new Date(Math.min(...occupancyDates)).toISOString();
    const endDate = addDays(new Date(Math.max(...occupancyDates)), 1).toISOString();
    // const endDate = new Date(Math.max(...occupancyDates)).toISOString();

    // validate room
    const roomCondition = { hotel: hotelId, isEnable: true };
    const roomOrder = { category: 1, no: 1 };
    const roomCursor = await Room.find(roomCondition).sort(roomOrder);
    const roomSpread = roomCursor.map((object) => {
      return { ...object._doc, _id: object.id };
    });

    // check availability of rooms from booked room of that category
    // Fetch all rooms booked during the requested period
    const pipeline = [
      {
        $match: {
          'hotel': new mongoose.Types.ObjectId(hotelId),
          status: { $nin: [ROOM_STATUS.CHECKED_OUT, ROOM_STATUS.ADVANCE_BOOKED] },
          $or: [{ startDate: { $lt: endDate } }, { endDate: { $gt: startDate } }]
        }
      },
      {
        $unwind: {
          path: '$rooms'
        }
      },
      {
        $sort: {
          occupancyDate: 1,
          room: 1
        }
      }
    ];
    const bookedRoomCursor = await Booking.aggregate(pipeline).exec();

    // Extract room IDs that are booked
    const bookedRoomIds = bookedRoomCursor.flatMap((bookedRoom) => {
      bookedRoom.rooms.map((object) => {
        return object.room.toString();
      })
    });

    // Fetch all rooms in the hotel and exclude the booked ones
    let availableRooms = [];

    if (bookedRoomIds.length > 0) {
      const bookedRoomSet = new Set(bookedRoomIds);
      availableRooms = roomSpread.filter((object) => {
        return !bookedRoomSet.has(object.room);
      });
    }
    else {
      availableRooms = [...roomSpread];
    }

    if (availableRooms.length > 0) {
      const roomsInput = [];
      const roomNos = [];
      let roomNoString = "";
      let dueAmount = 0;

      for (const object of rooms) {
        roomNos.push(object.no);
        occupancyDates.push(subDays(new Date(stringToDbDate(object.occupancyDate)), 1));

        const { cGSTPercentage, sGSTPercentage } = await GSTPercentageFind(object.tariff);
        const cGSTAmount = ((Number(parseFloat(object.tariff).toFixed(2)) * Number(parseFloat(cGSTPercentage).toFixed(2))) / 100);
        const sGSTAmount = ((Number(parseFloat(object.tariff).toFixed(2)) * Number(parseFloat(sGSTPercentage).toFixed(2))) / 100);
        dueAmount = Number(parseFloat(dueAmount).toFixed(2)) +
          Number(parseFloat(object.tariff).toFixed(2)) +
          Number(parseFloat(cGSTAmount).toFixed(2)) +
          Number(parseFloat(sGSTAmount).toFixed(2));
        dueAmount = Math.round(dueAmount);

        roomsInput.push({
          room: object.room,
          guestCount: object.guestCount,
          extraPersonCount: object.extraPersonCount,
          extraBedCount: object.extraBedCount,
          discount: Number(parseFloat(object.discount).toFixed(2)),
          tariff: Number(parseFloat(object.tariff).toFixed(2)),
          cGSTPercentage: Number(parseFloat(cGSTPercentage).toFixed(2)),
          sGSTPercentage: Number(parseFloat(sGSTPercentage).toFixed(2)),
          cGSTAmount: Number(parseFloat(cGSTAmount).toFixed(2)),
          sGSTAmount: Number(parseFloat(sGSTAmount).toFixed(2)),
          occupancyDate: subDays(new Date(stringToDbDate(object.occupancyDate)), 1).toISOString(),
          actualAccommodation: object.actualAccommodation,
          actualTariff: Number(parseFloat(object.actualTariff).toFixed(2)),
          actualExtraPersonTariff: Number(parseFloat(object.actualExtraPersonTariff).toFixed(2)),
          actualExtraBedTariff: Number(parseFloat(object.actualExtraBedTariff).toFixed(2)),
          actualMaxDiscount: Number(parseFloat(object.actualMaxDiscount).toFixed(2)),
          breakfastGuestCount: object.breakfastGuestCount
        });
      }

      const roomNoSet = [...new Set(roomNos)];
      roomNoSet.map((object) => {
        if (roomNoString.length > 0) roomNoString = roomNoString + ',' + object;
        else roomNoString = object;
      });

      const addData = new Booking({
        hotel: hotelId,
        employees: employeeId,
        guest: guestId,
        plan: planId,
        agent: agentId,
        roomNos: roomNoString,
        startDate: startDate,
        endDate: endDate,
        dueAmount: dueAmount,
        rooms: roomsInput,
        status: ROOM_STATUS.BOOKED
      });
      const addObject = await addData.save();
      if (!addObject) throw new customError(ERR_MSG.BOOKING_NOT_SAVE, ERR_CODE.INTERNAL);

      rooms.map(async (object) => {
        await Room.findByIdAndUpdate(object.room, { status: ROOM_STATUS.BOOKED })
      });

      // find data
      const findCondition = { _id: addObject._id, hotel: hotelId };
      const findCursor = await Booking.findOne(findCondition);
      const spread = {
        ...findCursor._doc,
        _id: findCursor.id,
        hotel: findCursor.hotel.toString(),
        employees: findCursor.employees.map((employee) => employee.toString()),
        guest: findCursor.guest.toString(),
        plan: findCursor.plan.toString(),
        agent: findCursor.agent.toString()
      };

      return spread;
    } else {
      throw new customError(ERR_MSG.ROOM_NOT_AVAILABLE, ERR_CODE.NOT_ALLOWED);
    }
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModBooking = async (hotelId, employeeId, id, guestId, planId, agentId, rooms) => {
  const FUNCTION_NAME = "mongoModBooking";

  try {
    // validate booking
    const bookingCondition = { _id: id, hotel: hotelId, status: { $ne: ROOM_STATUS.CHECKED_OUT } };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // validate guest
    const guestCondition = { _id: guestId, hotel: hotelId, isEnable: true };
    const guestCursor = await Guest.findOne(guestCondition);
    if (!guestCursor) throw new customError(ERR_MSG.INVALID_GUEST, ERR_CODE.BAD_REQUEST);

    // validate room
    const roomCondition = { hotel: hotelId, isEnable: true };
    const roomOrder = { category: 1, no: 1 };
    const roomCursor = await Room.find(roomCondition).sort(roomOrder);
    const roomSpread = roomCursor.map((object) => {
      return { ...object._doc, _id: object.id };
    });

    let dueAmount = bookingCursor.dueAmount;

    bookingCursor.rooms.map(async (object) => {
      dueAmount = dueAmount - (object.tariff + object.cGSTAmount + object.sGSTAmount);
      dueAmount = Math.round(dueAmount);

      await Room.findByIdAndUpdate(object.room, { status: ROOM_STATUS.EMPTY });
    });

    let occupancyDates = [];
    for (const object of rooms) {
      occupancyDates.push(subDays(new Date(stringToDbDate(object.occupancyDate)), 1));
    }

    const startDate = new Date(Math.min(...occupancyDates)).toISOString();
    const endDate = addDays(new Date(Math.max(...occupancyDates)), 1).toISOString();

    // check availability of rooms from booked room of that category
    // Fetch all rooms booked during the requested period
    const pipeline = [
      {
        $match: {
          '_id': { $ne: new mongoose.Types.ObjectId(id) },
          'hotel': new mongoose.Types.ObjectId(hotelId),
          status: { $nin: [ROOM_STATUS.CHECKED_OUT, ROOM_STATUS.ADVANCE_BOOKED] },
          $or: [{ startDate: { $lt: endDate } }, { endDate: { $gt: startDate } }]
        }
      },
      {
        $unwind: {
          path: '$rooms'
        }
      },
      {
        $sort: {
          occupancyDate: 1,
          room: 1
        }
      }
    ];
    const bookedRoomCursor = await Booking.aggregate(pipeline).exec();
    const bookedRoomIds = bookedRoomCursor.flatMap((bookedRoom) => {
      bookedRoom.rooms.map((object) => {
        return object.room.toString();
      })
    });

    let availableRooms = [];

    if (bookedRoomIds.length > 0) {
      const bookedRoomSet = new Set(bookedRoomIds);
      availableRooms = roomSpread.filter((object) => {
        return !bookedRoomSet.has(object.room);
      });
    }
    else {
      availableRooms = [...roomSpread];
    }

    if (availableRooms.length > 0) {
      const roomsInput = [];
      const roomNos = [];
      let roomNoString = "";

      for (const object of rooms) {
        roomNos.push(object.no);

        const roomCondition = { _id: object.room, hotel: hotelId, isEnable: true }
        const roomCursor = await Room.findOne(roomCondition);

        const { cGSTPercentage, sGSTPercentage } = await GSTPercentageFind(Number(parseFloat(roomCursor.tariff).toFixed(2)));
        const cGSTAmount = (Number(parseFloat(roomCursor.tariff).toFixed(2)) * Number(parseFloat(cGSTPercentage).toFixed(2))) / 100;
        const sGSTAmount = ((Number(parseFloat(roomCursor.tariff).toFixed(2)) * Number(parseFloat(sGSTPercentage).toFixed(2))) / 100);
        dueAmount = Number(parseFloat(dueAmount).toFixed(2)) +
          Number(parseFloat(roomCursor.tariff).toFixed(2)) +
          Number(parseFloat(cGSTAmount).toFixed(2)) +
          Number(parseFloat(sGSTAmount).toFixed(2));
        dueAmount = Math.round(dueAmount);

        roomsInput.push({
          room: roomCursor._id,
          guestCount: object.guestCount,
          extraPersonCount: object.extraPersonCount,
          extraBedCount: object.extraBedCount,
          discount: Number(parseFloat(object.discount).toFixed(2)),
          tariff: Number(parseFloat(object.tariff).toFixed(2)),
          cGSTPercentage: Number(parseFloat(cGSTPercentage).toFixed(2)),
          cGSTAmount: Number(parseFloat(cGSTAmount).toFixed(2)),
          sGSTPercentage: Number(parseFloat(sGSTPercentage).toFixed(2)),
          sGSTAmount: Number(parseFloat(sGSTAmount).toFixed(2)),
          occupancyDate: subDays(new Date(stringToDbDate(object.occupancyDate)), 1).toISOString(),
          actualAccommodation: object.actualAccommodation,
          actualTariff: Number(parseFloat(object.actualTariff).toFixed(2)),
          actualExtraPersonTariff: Number(parseFloat(object.actualExtraPersonTariff).toFixed(2)),
          actualExtraBedTariff: Number(parseFloat(object.actualExtraBedTariff).toFixed(2)),
          actualMaxDiscount: Number(parseFloat(object.actualMaxDiscount).toFixed(2)),
          breakfastGuestCount: object.guestCount
        });
      }

      const roomNoSet = [...new Set(roomNos)];
      roomNoSet.map((object) => {
        if (roomNoString.length > 0) roomNoString = roomNoString + ',' + object;
        else roomNoString = object;
      });

      const modData = {
        employees: employeeId,
        guest: guestId,
        plan: planId,
        agent: agentId,
        roomNos: roomNoString,
        startDate: startDate,
        endDate: endDate,
        dueAmount: dueAmount,
        rooms: roomsInput,
        status: ROOM_STATUS.BOOKED
      };
      const modObject = await Booking.findByIdAndUpdate(id, modData, { new: true });
      if (!modObject) throw new customError(ERR_MSG.BOOKING_NOT_SAVE, ERR_CODE.INTERNAL);

      rooms.map(async (object) => {
        const modRoomData = { status: ROOM_STATUS.BOOKED }
        const modRoomObject = await Room.findByIdAndUpdate(object.room, modRoomData, { new: true });
      });

      // read db
      const findCondition = { hotel: hotelId, _id: modObject.id, };
      const findCursor = await Booking.findOne(findCondition);
      const spread = { ...findCursor._doc, _id: findCursor.id };

      return spread;
    } else {
      throw new customError(ERR_MSG.ROOM_NOT_AVAILABLE, ERR_CODE.NOT_ALLOWED);
    }
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelBooking = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelBooking";

  try {
    // validate booking
    const bookingCondition = { _id: id, hotel: hotelId, status: { $nin: [ROOM_STATUS.CHECKED_OUT, ROOM_STATUS.ADVANCE_BOOKED] } };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const bookingSpread = {
      ...bookingCursor._doc,
      _id: bookingCursor.id,
      hotel: bookingCursor.hotel.toString(),
      guest: bookingCursor.guest.toString(),
      plan: bookingCursor.plan.toString(),
      agent: bookingCursor.agent.toString()
    };

    // delete guest
    const guestDelCondition = { _id: bookingCursor.guest, hotel: hotelId };
    const guestDelObject = await Guest.deleteOne(guestDelCondition);
    if (!guestDelObject) throw new customError(ERR_MSG.GUEST_NOT_DELETE, ERR_CODE.INTERNAL);

    // delete booking
    const bookingDelCondition = { _id: id, hotel: hotelId };
    const bookingDelObject = await Booking.deleteOne(bookingDelCondition);
    if (!bookingDelObject) throw new customError(ERR_MSG.BOOKING_NOT_DELETE, ERR_CODE.INTERNAL);

    bookingCursor.rooms.map(async (object) => {
      await Room.findByIdAndUpdate(object.room, { status: ROOM_STATUS.EMPTY });
    });

    return bookingSpread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// get all room sales for a period
const mongoRoomSales = async (hotelId, employeeId, startDate, endDate) => {
  const FUNCTION_NAME = "mongoRoomSales";

  try {
    const pipeline = [
      {
        $match: {
          'hotel': new mongoose.Types.ObjectId(hotelId),
          'status': { $ne: ROOM_STATUS.ADVANCE_BOOKED },
          'startDate': { $gte: new Date(startDate) },
          'endDate': { $lte: new Date(endDate) }
        }
      },
      {
        $unwind: {
          path: '$rooms'
        }
      },
      {
        $group: {
          _id: {
            room: '$rooms.room',
            occupancyDate: {
              $dateToString: {
                format: '%Y-%m-%d',
                date: '$rooms.occupancyDate'
              }
            }
          },
          tariff: {
            $sum: '$rooms.actualTariff'
          }
        }
      },
      {
        $project: {
          _id: 0,
          room: "$_id.room",
          occupancyDate: "$_id.occupancyDate",
          tariff: 1
        }
      },
      {
        $lookup: {
          from: 'rooms',
          localField: 'room',
          foreignField: '_id',
          as: 'roomDetails'
        }
      },
      {
        $unwind: {
          path: '$roomDetails',
          preserveNullAndEmptyArrays: true
        }
      },
      {
        $project: {
          room: '$roomDetails.no',
          occupancyDate: 1,
          tariff: {
            $ifNull: ['$tariff', 0]
          }
        }
      },
      {
        $sort: {
          occupancyDate: 1,
          room: 1
        }
      }
    ];
    const cursor = await Booking.aggregate(pipeline).exec();
    if (!cursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = cursor.map((object) => {
      return {
        occupancyDate: object.occupancyDate,
        room: object.room,
        tariff: object.tariff
      };
    });

    return spread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// get all room sales for a period
const mongoGetCheckout = async (hotelId, employeeId, searchDate) => {
  const FUNCTION_NAME = "mongoGetCheckout";

  try {
    const condition = {
      hotel: hotelId,
      status: { $nin: [ROOM_STATUS.CHECKED_OUT, ROOM_STATUS.ADVANCE_BOOKED] },
      endDate: { $eq: new Date(searchDate) }
    };
    const order = { room: 1, endDate: 1 };
    const cursor = await Booking.find(condition).sort(order);
    if (!cursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString(),
        roomNos: object.roomNos,
        rooms: object.rooms.map((room) => {
          return {
            ...room._doc,
            _id: room.id,
            room: room.room.toString()
          };
        }),
        startDate: object.startDate.toISOString(),
        endDate: object.endDate.toISOString(),
        billNo: object.billNo ? object.billNo : "",
        dueAmount: object.dueAmount,
        status: object.status,
        createdAt: object.createdAt.toISOString(),
        updatedAt: object.updatedAt.toISOString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  mongoBookings, mongoSearchBooking, mongoGetBooking, mongoAddBooking,
  mongoModBooking, mongoDelBooking, mongoRoomSales, mongoGetCheckout
};