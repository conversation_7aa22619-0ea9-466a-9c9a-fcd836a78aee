const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");


// Function to get one id data from Redis
const getOneIdentificationRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_IDENTIFICATION}_${hotelId}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all ids data from Redis
const getAllIdentificationRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_IDENTIFICATION}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(hashKeys.map(
            async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set food data in Redis
const setOneIdentificationRedis = async (hotelId, data) => {
    try {
        await delOneIdentificationRedis(hotelId, data._id);

        await addHashValues(`${process.env.HASH_IDENTIFICATION}_${hotelId}:${data._id}`,
            {
                _id: data._id,
                hotel: data.hotel,
                name: data.name,
                description: data.description ? data.description : "",
                isEnable: data.isEnable,
                createdAt: data.createdAt,
                updatedAt: data.updatedAt
            }
        );
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set id data in Redis
const setAllIdentificationRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllIdentificationRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_IDENTIFICATION}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        description: data.description ? data.description : "",
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                )
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete id data from Redis
const delOneIdentificationRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_IDENTIFICATION}_${hotelId}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete id data from Redis
const delAllIdentificationRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_IDENTIFICATION}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey);
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};


module.exports = {
    getOneIdentificationRedis, getAllIdentificationRedis, setOneIdentificationRedis,
    setAllIdentificationRedis, delOneIdentificationRedis, delAllIdentificationRedis
};
