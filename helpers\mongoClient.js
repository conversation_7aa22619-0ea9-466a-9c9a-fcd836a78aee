const mongoose = require("mongoose");


// Connect to the MongoDB database using Mongoose
const connectMongo = async () => {
    try {
        const uri = `mongodb+srv://${process.env.DB_MONGO_USER}:${process.env.DB_MONGO_PASSWORD}@clubcommcluster.cy8qctn.mongodb.net/${process.env.DB_MONGO_NAME}?retryWrites=true&w=majority`;

        await mongoose.connect(uri);
        return true;
    } catch (error) {
        console.log("MongoDB connection error:", error.message);
        return false;
    }
};

// Check if MongoDB server is connected
const isMongoConnected = () => {
    try {
        return mongoose.connection.readyState === 1;
    } catch (error) {
        console.error("Error checking MongoDB connection:", error.message);
        return false;
    }
};

// Test MongoDB connection
const testMongoConnection = async () => {
    try {
        // Check if already connected
        if (mongoose.connection.readyState === 1) {
            return true;
        }

        // If not connected, try to connect
        const uri = `mongodb+srv://${process.env.DB_MONGO_USER}:${process.env.DB_MONGO_PASSWORD}@clubcommcluster.cy8qctn.mongodb.net/${process.env.DB_MONGO_NAME}?retryWrites=true&w=majority`;

        await mongoose.connect(uri);
        return true;
    } catch (error) {
        console.error("MongoDB connection test failed:", error.message);
        return false;
    }
};

process.on("unhandledRejection", (reason) => {
    console.error("Unhandled Promise Rejection:", reason);
});


module.exports = { connectMongo, isMongoConnected, testMongoConnection };