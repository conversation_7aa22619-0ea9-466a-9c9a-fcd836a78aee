const { subDays } = require("date-fns");
const Hotel = require("../../models/hotel");
const Table = require("../../models/table");
const { mongoGetAdvancePayment, mongoAddAdvancePayment, mongoModAdvancePayment,
  mongoDelAdvancePayment, mongoBillPayments, mongoSearchBillPayment,
  mongoGetBillPayment, mongoAddBillPayment, mongoModBillPayment, mongoDelBillPayment,
  mongoAttachBill } = require("./mongo/paymentMongo");
const { setOneHotelRedis } = require("../../helpers/redis/hotel");
const { setAllTableRedis } = require("../../helpers/redis/table");
const { advancePaymentDetail, attachedBillDetail, paymentDetail } = require("../../helpers/db");
const { isAuthorized } = require("../../helpers/authorize");
const { stringToDbDate } = require("../../helpers/date");
const { stringToBoolean } = require("../../helpers/boolean");
const { writeActivityLog, writeErrLog } = require("../../helpers/log");
const { customError } = require('../../helpers/customError');
const { MODEL_LIST, OPERATION_LIST } = require("../../configs/permissionOptions");
const { ACTIVITY_LIST } = require("../../configs/activityOptions");
const { ERR_CODE, ERR_MSG } = require("../../configs/messageOptions");
const FILE_NAME = "payments.js";


// find a document by id from a collection
const getAdvancePayment = async (args, req) => {
  const FUNCTION_NAME = "getAdvancePayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get all data
    const getObject = await mongoGetAdvancePayment(hotelId, employeeId, _id);
    if (!getObject) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_ADVANCE_GET);

    // return output
    return getObject.map(async (object) => {
      return await advancePaymentDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addAdvancePayment = async (args, req) => {
  const FUNCTION_NAME = "addAdvancePayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, mode, amount, particular } = args.paymentInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_BILL, ERR_CODE.BAD_REQUEST);
    if (mode === "") throw new customError(ERR_MSG.INVALID_PAYMENT_MODE, ERR_CODE.BAD_REQUEST);
    if (amount <= 0) throw new customError(ERR_MSG.INVALID_AMOUNT, ERR_CODE.BAD_REQUEST);

    // add advance payment
    const addObject = await mongoAddAdvancePayment(hotelId, employeeId, _id, mode, amount, particular);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      // update hotel
      const hotelCondition = { _id: hotelId, isEnable: true };
      const hotelCursor = await Hotel.findOne(hotelCondition);
      const hotelSpread = { ...hotelCursor._doc, _id: hotelCursor.id };
      await setOneHotelRedis(hotelId, hotelSpread);
    }

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_ADVANCE_ADD);

    // return output
    return await advancePaymentDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// change a document into the collection
const modAdvancePayment = async (args, req) => {
  const FUNCTION_NAME = "modAdvancePayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, amount, mode, particular } = args.paymentInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_BILL, ERR_CODE.BAD_REQUEST);
    if (amount <= 0) throw new customError(ERR_MSG.INVALID_AMOUNT, ERR_CODE.BAD_REQUEST);
    if (mode === "") throw new customError(ERR_MSG.INVALID_PAYMENT_MODE, ERR_CODE.BAD_REQUEST);

    // modify advance payment
    const modObject = await mongoModAdvancePayment(hotelId, employeeId, _id, mode, amount, particular);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      // update hotel
      const hotelCondition = { _id: hotelId, isEnable: true };
      const hotelCursor = await Hotel.findOne(hotelCondition);
      const hotelSpread = { ...hotelCursor._doc, _id: hotelCursor.id };
      await setOneHotelRedis(hotelId, hotelSpread);
    }

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_ADVANCE_ADD);

    // return output
    return await advancePaymentDetail(modObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from the collection
const delAdvancePayment = async (args, req) => {
  const FUNCTION_NAME = "delAdvancePayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);

    // delete advance payment
    const delObject = await mongoDelAdvancePayment(hotelId, employeeId, _id);

    // add activity 
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_DEL);

    // return output
    return await advancePaymentDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// get all documents of a collection
const billPayments = async (args, req) => {
  const FUNCTION_NAME = "billPayments";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // get all data
    const allObjects = await mongoBillPayments(hotelId, employeeId);
    if (!allObjects) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_LIST);

    // return output
    return allObjects.map(async (object) => {
      return await paymentDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const searchBillPayment = async (args, req) => {
  const FUNCTION_NAME = "searchBillPayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { startDate, endDate } = args.paymentSearchInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    const startDateInput =
      startDate === ""
        ? new Date()
        : subDays(new Date(stringToDbDate(startDate)), 1).toISOString();

    const endDateInput =
      endDate === ""
        ? new Date()
        : new Date(stringToDbDate(endDate)).toISOString();

    // search data
    const searchObjects = await mongoSearchBillPayment(hotelId, employeeId, startDateInput, endDateInput);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_SEARCH);

    // return output
    return searchObjects.map(async (object) => {
      return await paymentDetail(object);
    });
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const getBillPayment = async (args, req) => {
  const FUNCTION_NAME = "getBillPayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.VIEW))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (!_id) throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.CONFLICT);

    // get payment
    const getObject = await mongoGetBillPayment(hotelId, employeeId, _id);
    if (!getObject) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_GET);

    // return output
    return await paymentDetail(getObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const addBillPayment = async (args, req) => {
  const FUNCTION_NAME = "addBillPayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, amount, mode, particular } = args.paymentInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_BILL, ERR_CODE.BAD_REQUEST);
    if (amount <= 0) throw new customError(ERR_MSG.INVALID_AMOUNT, ERR_CODE.BAD_REQUEST);
    if (mode === "") throw new customError(ERR_MSG.INVALID_PAYMENT_MODE, ERR_CODE.BAD_REQUEST);

    // add payment
    const addObject = await mongoAddBillPayment(hotelId, employeeId, _id, amount, mode, particular);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      // update hotel
      const hotelCondition = { _id: hotelId, isEnable: true };
      const hotelCursor = await Hotel.findOne(hotelCondition);
      const hotelSpread = { ...hotelCursor._doc, _id: hotelCursor.id };
      await setOneHotelRedis(hotelSpread);

      // update table
      const tableCondition = { hotel: hotelId, isEnable: true };
      const tableOrder = { no: 1 };
      const tableCursor = await Table.find(tableCondition).sort(tableOrder);
      const tableSpread = tableCursor.map((object) => {
        return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
      });
      await setAllTableRedis(hotelId, tableSpread);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_ADD);

    // return output
    return await paymentDetail(addObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const modBillPayment = async (args, req) => {
  const FUNCTION_NAME = "modBillPayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, amount, mode, particular } = args.paymentInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.EDIT))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_ID, ERR_CODE.BAD_REQUEST);
    if (amount <= 0) throw new customError(ERR_MSG.INVALID_AMOUNT, ERR_CODE.BAD_REQUEST);
    if (mode === "") throw new customError(ERR_MSG.INVALID_PAYMENT_MODE, ERR_CODE.BAD_REQUEST);

    // modify payment
    const modObject = await mongoModBillPayment(hotelId, employeeId, _id, amount, mode, particular);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { hotel: hotelId, isEnable: true };
      const order = { no: 1 };
      const cursor = await Table.find(condition).sort(order);
      const spread = cursor.map((object) => {
        return { ...cursor._doc, _id: cursor.id, hotel: cursor.hotel.toString() };
      });

      await setAllTableRedis(hotelId, spread);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_MOD);

    return await paymentDetail(modObject);
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const delBillPayment = async (args, req) => {
  const FUNCTION_NAME = "delBillPayment";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const _id = args._id;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.REMOVE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // delete payment
    const delObject = await mongoDelBillPayment(hotelId, employeeId, _id);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { hotel: hotelId, isEnable: true };
      const order = { no: 1 };
      const cursor = await Table.find(condition).sort(order);
      const spread = cursor.map((object) => {
        return { ...cursor._doc, _id: cursor.id, hotel: cursor.hotel.toString() };
      });

      await setAllTableRedis(hotelId, spread);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.PAYMENT_DEL);

    // return output
    return await paymentDetail(delObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const attachBill = async (args, req) => {
  const FUNCTION_NAME = "attachBill";
  const { hotelId, employeeId, employeeRole, isAuthenticated } = req;
  const { _id, bookingId } = args.billAttachInput;

  try {
    // check for authentication and authorization
    if (!isAuthenticated) throw new customError(ERR_MSG.FORBIDDEN, ERR_CODE.FORBIDDEN);
    if (!await isAuthorized(employeeRole, MODEL_LIST.PAYMENT, OPERATION_LIST.CREATE))
      throw new customError(ERR_MSG.UNAUTHORIZE, ERR_CODE.UNAUTHORIZE);

    // validate inputs
    if (_id === "") throw new customError(ERR_MSG.INVALID_BILL, ERR_CODE.BAD_REQUEST);
    if (bookingId === "") throw new customError(ERR_MSG.INVALID_BOOKING, ERR_CODE.BAD_REQUEST);

    // attach bill
    const attachObject = await mongoAttachBill(hotelId, employeeId, _id, bookingId);

    // Redis
    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      // update table
      const tableCondition = { hotel: hotelId, isEnable: true };
      const tableOrder = { no: 1 };
      const tableCursor = await Table.find(tableCondition).sort(tableOrder);
      const tableSpread = tableCursor.map((object) => {
        return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
      });
      await setAllTableRedis(hotelId, tableSpread);
    }

    // record activity in log
    writeActivityLog(hotelId, employeeId, ACTIVITY_LIST.BILL_ATTACH);

    // return output
    return await attachedBillDetail(attachObject);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  getAdvancePayment, addAdvancePayment, modAdvancePayment,
  delAdvancePayment, billPayments, searchBillPayment, getBillPayment,
  addBillPayment, modBillPayment, delBillPayment, attachBill
};