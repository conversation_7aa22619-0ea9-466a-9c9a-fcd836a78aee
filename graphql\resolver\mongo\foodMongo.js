const Food = require("../../../models/food");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "foodMongo.js";


// get all documents of a collection
const mongoFoods = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoFoods";

  try {
    // read all document from db
    const condition = { hotel: hotelId, isEnable: true };
    const order = { name: 1 };
    const cursor = await Food.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchFood = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "mongoSearchFood";
  const order = { name: 1 };
  let condition = null;

  try {
    if (!searchKey.trim()) {
      condition = { hotel: hotelId, isEnable: true };
    }
    else {
      condition = { hotel: hotelId, isEnable: true, $text: { $search: searchKey } };
    }

    const cursor = await Food.find(condition).sort(order);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() }
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const mongoGetFood = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetFood";
  let spread = null;

  try {
    // read single data
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Food.findOne(condition);
    if (cursor) spread = { ...cursor._doc, _id: cursor.id.toString(), hotel: cursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddFood = async (hotelId, employeeId, name, unitPrice, description) => {
  const FUNCTION_NAME = "mongoAddFood";

  try {
    // check for duplicate data in db
    const condition = { hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Food.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.FOOD_CONFLICT, ERR_CODE.CONFLICT);

    // insert data in db
    const data = {
      hotel: hotelId,
      name: name,
      unitPrice: unitPrice,
      description: description
    };
    const addData = new Food(data);
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.FOOD_NOT_SAVE, ERR_CODE.INTERNAL);

    // find data
    const findCondition = { hotel: hotelId, _id: addObject.id };
    const findCursor = await Food.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModFood = async (hotelId, employeeId, id, name, unitPrice, description) => {
  const FUNCTION_NAME = "mongoModFood";

  try {
    // check for duplicate data in db
    const condition = { _id: { $not: { $eq: id } }, hotel: hotelId, name: name, isEnable: true };
    const duplicateCursor = await Food.findOne(condition);
    if (duplicateCursor) throw new customError(ERR_MSG.FOOD_CONFLICT, ERR_CODE.CONFLICT);

    // change data in db
    const modData = {
      name: name,
      unitPrice: unitPrice,
      description: description
    };
    const modObject = await Food.findByIdAndUpdate(id, modData, { new: true });
    if (!modObject) throw new customError(ERR_MSG.FOOD_NOT_SAVE, ERR_CODE.NOT_EXISTS);

    // find data
    const findCondition = { hotel: hotelId, _id: modObject.id };
    const findCursor = await Food.findOne(findCondition);
    const spread = { ...findCursor._doc, _id: findCursor.id.toString(), hotel: findCursor.hotel.toString() };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelFood = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoDelFood";

  try {
    // check for existence
    const condition = { _id: id, hotel: hotelId, isEnable: true };
    const cursor = await Food.findOne(condition);
    const spread = { ...cursor._doc, _id: cursor.id.toString(), hotel: cursor.hotel.toString() };
    if (!spread) throw new customError(ERR_MSG.FOOD_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // delete from db
    const delObject = await Food.findByIdAndUpdate(id, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.AGENT_NOT_DELETE, ERR_CODE.NOT_ALLOWED);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Delete a document from the collection
const mongoDelFoods = async (hotelId, employeeId, ids) => {
  const FUNCTION_NAME = "mongoDelFoods";

  try {
    // read all agents from db
    const condition = { _id: { $in: ids }, hotel: hotelId, isEnable: true };
    const cursor = await Food.find(condition);
    if (cursor.length !== ids.length) throw new customError(ERR_MSG.FOOD_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // spread data
    const spread = cursor.map((object) => {
      return { ...object._doc, _id: object.id.toString(), hotel: object.hotel.toString() };
    });

    // delete from db
    const delArray = await Food.updateMany({ _id: { $in: ids } }, { isEnable: false });
    if (!delArray) throw new customError(ERR_MSG.FOOD_NOT_DELETE, ERR_CODE.INTERNAL);

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  mongoFoods, mongoSearchFood, mongoGetFood, mongoAddFood,
  mongoModFood, mongoDelFood, mongoDelFoods
};