const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneRoomRedis } = require("../../../helpers/redis/room");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "roomRedis.js";


// get all documents of a collection
const redisRooms = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisRooms";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROOM_HOTEL_FILTER}`,
      query,
      'RETURN', '10', '_id', 'hotel', 'category', 'no', 'accommodation', 'tariff', 'extraBedTariff', 'extraPersonTariff', 'maxDiscount', 'status',
      'SORTBY', 'no', 'ASC',
      'LIMIT', '0', '100'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchRoom = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchRoom";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROOM_HOTEL_FILTER}`,
      query,
      'RETURN', '10', '_id', 'hotel', 'category', 'no', 'accommodation', 'tariff', 'extraBedTariff', 'extraPersonTariff', 'maxDiscount', 'status',
      'SORTBY', 'no', 'ASC',
      'LIMIT', '0', '100'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.no) ||
          regex.test(item.accommodation)) ||
          regex.test(item.tariff) ||
          regex.test(item.extraBedTariff) ||
          regex.test(item.extraPersonTariff) ||
          regex.test(item.maxDiscount);

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetRoom = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetRoom";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_ROOM_UNIQUE}`,
      query,
      'RETURN', '10', '_id', 'hotel', 'category', 'no', 'accommodation', 'tariff', 'extraBedTariff', 'extraPersonTariff', 'maxDiscount', 'status',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetRoom = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetRoom";

  try {
    // read single data
    await setOneRoomRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelRoom = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelRoom";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_ROOM}_${hotelId}:${id}`);
    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { redisRooms, redisSearchRoom, redisGetRoom, redisSetRoom, redisDelRoom };