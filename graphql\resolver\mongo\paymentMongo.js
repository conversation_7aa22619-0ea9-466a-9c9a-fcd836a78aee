const Hotel = require("../../../models/hotel");
const Table = require("../../../models/table");
const Booking = require("../../../models/booking");
const Order = require("../../../models/order");
const Bill = require("../../../models/bill");
const Payment = require("../../../models/payment");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { TABLE_STATUS } = require("../../../configs/tableOptions");
const { ORDER_TYPE } = require("../../../configs/orderOptions");
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "paymentMongo.js";


// find a document by id from a collection
const mongoGetAdvancePayment = async (hotelId, employeeId, bookingId) => {
  const FUNCTION_NAME = "mongoGetAdvancePayment";

  try {
    // get all data
    const condition = { booking: bookingId, hotel: hotelId, isEnable: true };
    const order = { updatedAt: 1 };
    const cursor = await Payment.find(condition).sort(order);
    if (!cursor) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employee: object.employee.toString(),
        booking: object.booking.toString(),
        mode: object.mode.toString()
      }
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddAdvancePayment = async (hotelId, employeeId, bookingId, paymentModeId, amount, particular) => {
  const FUNCTION_NAME = "mongoAddAdvancePayment";

  try {
    // find booking
    const bookingCondition = { _id: bookingId, hotel: hotelId };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    if (bookingCursor.dueAmount < amount) throw new customError(ERR_MSG.INVALID_AMOUNT, ERR_CODE.BAD_REQUEST);

    // find hotel
    const hotelCondition = { _id: hotelId, isEnable: true };
    const hotelCursor = await Hotel.findOne(hotelCondition);
    const hotelSpread = { ...hotelCursor._doc, _id: hotelCursor.id };
    const receiptNo = parseInt(hotelSpread.lastReceiptNo) + 1;

    // update last food bill no in hotel document
    const modHotelObject = await Hotel.findByIdAndUpdate(hotelId, { lastReceiptNo: receiptNo });
    if (!modHotelObject) throw new customError(ERR_MSG.HOTEL_NOT_SAVE, ERR_CODE.INTERNAL);

    // add payment
    const addData = new Payment({
      hotel: hotelId,
      employee: employeeId,
      booking: bookingId,
      receiptNo: receiptNo,
      amount: parseFloat(amount).toFixed(2),
      mode: paymentModeId,
      particular: particular
    });
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.PAYMENT_NOT_SAVE, ERR_CODE.INTERNAL);

    let dueAmount = Number(parseFloat(bookingCursor.dueAmount).toFixed(2)) - Number(parseFloat(amount).toFixed(2));
    dueAmount = Math.round(dueAmount);

    // update booking
    await Booking.findByIdAndUpdate(bookingId,
      { dueAmount: dueAmount });

    // find payment
    const condition = { _id: addObject._id.toString(), hotel: hotelId };
    const cursor = await Payment.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking.toString(),
      mode: cursor.mode.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// change a document into the collection
const mongoModAdvancePayment = async (hotelId, employeeId, paymentId, paymentModeId, amount, particular) => {
  const FUNCTION_NAME = "mongoModAdvancePayment";

  try {
    // find payment
    const paymentCondition = { _id: paymentId, hotel: hotelId, isEnable: true };
    const paymentCursor = await Payment.findOne(paymentCondition);
    if (!paymentCursor) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // find hotel
    const hotelCondition = { _id: hotelId, isEnable: true };
    const hotelCursor = await Hotel.findOne(hotelCondition);
    const receiptNo = parseInt(hotelCursor.lastReceiptNo, 10) + 1;

    // delete payment
    const delObject = await Payment.findByIdAndUpdate(paymentId, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.PAYMENT_NOT_DELETE, ERR_CODE.INTERNAL);

    // update last food bill no in hotel document
    const modHotelObject = await Hotel.findByIdAndUpdate(hotelId, { lastReceiptNo: receiptNo });
    if (!modHotelObject) throw new customError(ERR_MSG.HOTEL_NOT_SAVE, ERR_CODE.INTERNAL);

    // find booking
    const bookingCondition = { _id: paymentCursor.booking, hotel: hotelId };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // add payment
    const addData = new Payment({
      hotel: hotelId,
      employee: employeeId,
      booking: paymentCursor.booking,
      receiptNo: receiptNo,
      amount: parseFloat(amount).toFixed(2),
      mode: paymentModeId,
      particular: particular
    });
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.PAYMENT_NOT_SAVE, ERR_CODE.INTERNAL);

    let dueAmount = Number(parseFloat(bookingCursor.dueAmount).toFixed(2)) + Number(parseFloat(paymentCursor.amount).toFixed(2)) - Number(parseFloat(amount).toFixed(2));
    dueAmount = Math.round(dueAmount);

    // update booking
    await Booking.findByIdAndUpdate(paymentCursor.booking,
      { dueAmount: dueAmount }
    );

    // find data
    const condition = { _id: addObject._id, hotel: hotelId };
    const cursor = await Payment.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking.toString(),
      mode: cursor.mode.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from the collection
const mongoDelAdvancePayment = async (hotelId, employeeId, paymentId) => {
  const FUNCTION_NAME = "mongoDelAdvancePayment";

  try {
    // find payment
    const paymentCondition = { _id: paymentId, hotel: hotelId, isEnable: true };
    let paymentCursor = await Payment.findOne(paymentCondition);
    if (!paymentCursor) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const paymentSpread = {
      ...paymentCursor._doc,
      _id: paymentCursor.id,
      hotel: paymentCursor.hotel.toString(),
      employee: paymentCursor.employee.toString(),
      booking: paymentCursor.booking.toString(),
      mode: paymentCursor.mode.toString()
    };

    // delete payment
    const delObject = await Payment.findByIdAndUpdate(paymentId, { isEnable: false });
    if (!delObject) throw new customError(ERR_MSG.PAYMENT_NOT_DELETE, ERR_CODE.INTERNAL);

    // update booking
    const bookingCondition = { _id: paymentCursor.booking, hotel: hotelId };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    let dueAmount = Number(parseFloat(bookingCursor.dueAmount).toFixed(2)) + Number(parseFloat(paymentCursor.amount).toFixed(2));
    dueAmount = Math.round(dueAmount);

    // update booking
    const modObject = await Booking.findByIdAndUpdate(paymentCursor.booking,
      { dueAmount: dueAmount }
    );
    if (!modObject) throw new customError(ERR_MSG.BILL_NOT_ATTACHED, ERR_CODE.INTERNAL);

    return paymentSpread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// get all documents of a collection
const mongoBillPayments = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoBillPayments";

  try {
    const condition = { hotel: hotelId, isEnable: true };
    const order = { updatedAt: 1 };
    const cursor = await Payment.find(condition).sort(order);
    if (!cursor) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employee: object.employee.toString(),
        booking: object.booking ? object.booking.toString() : null,
        bill: object.bill ? object.bill.toString() : null,
        mode: object.mode.toString()
      }
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const mongoSearchBillPayment = async (hotelId, employeeId, startDate, endDate) => {
  const FUNCTION_NAME = "mongoSearchBillPayment";

  try {
    const condition = {
      hotel: hotelId,
      updatedAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      isEnable: true
    };
    const order = { updatedAt: 1 };
    const cursor = await Payment.find(condition).sort(order);
    if (!cursor) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employee: object.employee.toString(),
        booking: object.booking ? object.booking.toString() : null,
        bill: object.bill ? object.bill.toString() : null,
        mode: object.mode.toString()
      };
    });

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetBillPayment = async (hotelId, employeeId, paymentId) => {
  const FUNCTION_NAME = "mongoGetBillPayment";

  try {
    const condition = { _id: paymentId, hotel: hotelId, isEnable: true };
    const cursor = await Payment.findOne(condition);
    if (!cursor) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: object.booking ? object.booking.toString() : null,
      bill: object.bill ? object.bill.toString() : null,
      mode: cursor.mode.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAddBillPayment = async (hotelId, employeeId, billId, amount, paymentModeId, particular) => {
  const FUNCTION_NAME = "mongoAddBillPayment";

  try {
    // get hotel data
    const hotelCondition = { _id: hotelId, isEnable: true };
    const hotelCursor = await Hotel.findOne(hotelCondition);
    const receiptNo = parseInt(hotelCursor.lastReceiptNo) + 1;

    // get bill data
    const billCondition = { _id: billId, hotel: hotelId };
    const billCursor = await Bill.findOne(billCondition);
    if (!billCursor) throw new customError(ERR_MSG.BILL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);

    // update last food bill no in hotel document
    const modHotelObject = await Hotel.findByIdAndUpdate(hotelId, { lastReceiptNo: receiptNo });
    if (!modHotelObject) throw new customError(ERR_MSG.HOTEL_NOT_SAVE, ERR_CODE.INTERNAL);

    // insert payment data
    const addData = new Payment({
      hotel: hotelId,
      employee: employeeId,
      bill: billId,
      receiptNo: receiptNo,
      amount: parseFloat(amount).toFixed(2),
      mode: paymentModeId,
      particular: particular
    });
    const addObject = await addData.save();
    if (!addObject) throw new customError(ERR_MSG.PAYMENT_NOT_SAVE, ERR_CODE.INTERNAL);

    // update bill
    const modBillObject = await Bill.findByIdAndUpdate(billId, { status: TABLE_STATUS.PAID });
    if (!modBillObject) throw new customError(ERR_MSG.BILL_NOT_SAVE, ERR_CODE.INTERNAL);

    if (billCursor.type === ORDER_TYPE.FOOD) {
      await Order.findByIdAndUpdate(billCursor.order, { status: TABLE_STATUS.PAID });

      billCursor.tables.map(async (table) => {
        await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.EMPTY })
      });
    }

    // find data
    const condition = { _id: addObject._id, hotel: hotelId };
    const cursor = await Payment.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      bill: cursor.bill ? cursor.bill.toString() : null,
      mode: cursor.mode.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModBillPayment = async (hotelId, employeeId, paymentId, amount, paymentModeId, particular) => {
  const FUNCTION_NAME = "mongoModBillPayment";

  try {
    // find payment
    const paymentCondition = { _id: paymentId, hotel: hotelId, isEnable: true };
    const paymentCursor = await Payment.findOne(paymentCondition);
    if (!paymentCursor) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const paymentSpread = {
      ...paymentCursor._doc,
      _id: paymentCursor.id,
      hotel: paymentCursor.hotel.toString(),
      employee: paymentCursor.employee.toString(),
      bill: paymentCursor.bill.toString(),
      mode: paymentCursor.mode.toString()
    };

    // find bill
    const billCondition = { _id: paymentSpread.bill, hotel: hotelId };
    const billCursor = await Bill.findOne(billCondition);
    if (!billCursor) throw new customError(ERR_MSG.PAYMENT_NOT_DELETE, ERR_CODE.INTERNAL);
    const billSpread = {
      ...billCursor._doc,
      _id: billCursor.id,
      hotel: billCursor.hotel.toString(),
      employee: billCursor.employee.toString(),
      order: billCursor.order.toString()
    };

    // find order
    const orderCondition = { _id: billSpread.order, hotel: hotelId };
    const orderCursor = await Order.findOne(orderCondition);
    if (!orderCursor) throw new customError(ERR_MSG.PAYMENT_NOT_DELETE, ERR_CODE.INTERNAL);
    const orderSpread = {
      ...orderCursor._doc,
      _id: orderCursor.id,
      hotel: orderCursor.hotel.toString(),
      employees: orderCursor.employees.map((employee) => employee.toString()),
      booking: orderCursor.booking ? orderCursor.booking.toString() : null,
      tables: orderCursor.tables ? orderCursor.tables.map((table) => table.toString()) : null
    };

    // change payment
    const modObject = await Payment.findByIdAndUpdate(paymentId, {
      employees: employeeId,
      amount: parseFloat(amount).toFixed(2),
      mode: paymentModeId,
      particular: particular
    });
    if (!modObject) throw new customError(ERR_MSG.PAYMENT_NOT_SAVE, ERR_CODE.INTERNAL);

    if (orderSpread.type === ORDER_TYPE.FOOD) {
      await Order.findByIdAndUpdate(orderSpread._id, { status: TABLE_STATUS.PAID });

      orderSpread.tables.map(async (table) => {
        await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.EMPTY });
      });
    }

    // find payment
    paymentSpread = {
      ...paymentCursor._doc,
      _id: paymentCursor.id,
      hotel: paymentCursor.hotel.toString(),
      employee: paymentCursor.employee.toString(),
      booking: object.booking ? object.booking.toString() : null,
      bill: object.bill ? object.bill.toString() : null,
      mode: paymentCursor.mode.toString()
    };

    return paymentSpread;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// delete a document from a collection
const mongoDelBillPayment = async (hotelId, employeeId, paymentId) => {
  const FUNCTION_NAME = "mongoDelBillPayment";

  try {
    // find payment
    const paymentCondition = { _id: paymentId, hotel: hotelId, isEnable: true };
    const paymentCursor = await Payment.findOne(paymentCondition);
    if (!paymentCursor) throw new customError(ERR_MSG.PAYMENT_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const paymentSpread = {
      ...paymentCursor._doc,
      _id: paymentCursor.id,
      hotel: paymentCursor.hotel.toString(),
      employee: paymentCursor.employee.toString(),
      booking: object.booking ? object.booking.toString() : null,
      bill: object.bill ? object.bill.toString() : null,
      mode: paymentCursor.mode.toString()
    };

    // find bill
    const billCondition = { _id: paymentSpread.bill, hotel: hotelId };
    const billCursor = await Bill.findOne(billCondition);
    if (!billCursor) throw new customError(ERR_MSG.PAYMENT_NOT_DELETE, ERR_CODE.INTERNAL);
    const billSpread = {
      ...billCursor._doc,
      _id: billCursor.id,
      hotel: billCursor.hotel.toString(),
      employee: billCursor.employee.toString(),
      order: billCursor.order.toString()
    };

    // find order
    const orderCondition = { _id: billSpread.order, hotel: hotelId };
    const orderCursor = await Order.findOne(orderCondition);
    if (!orderCursor) throw new customError(ERR_MSG.PAYMENT_NOT_DELETE, ERR_CODE.INTERNAL);
    const orderSpread = {
      ...orderCursor._doc,
      _id: orderCursor.id,
      hotel: orderCursor.hotel.toString(),
      employees: orderCursor.employees.map((employee) => employee.toString()),
      booking: orderCursor.booking ? orderCursor.booking.toString() : null,
      tables: orderCursor.tables ? orderCursor.tables.map((table) => table.toString()) : null
    };

    if (orderSpread.type === ORDER_TYPE.FOOD) {
      await Order.findByIdAndUpdate(orderSpread._id, { status: TABLE_STATUS.BILLED });

      orderSpread.tables.map(async (table) => {
        updateObject = await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.BILLED });
      });
    }

    return orderSpread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// insert a document into the collection
const mongoAttachBill = async (hotelId, employeeId, billId, bookingId) => {
  const FUNCTION_NAME = "mongoAttachBill";

  try {
    // check for existence
    const billCondition = { _id: billId, hotel: hotelId };
    const billCursor = await Bill.findOne(billCondition);
    if (!billCursor) throw new customError(ERR_MSG.BILL_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const billSpread = {
      ...billCursor._doc,
      _id: billCursor.id,
      hotel: billCursor.hotel.toString(),
      employee: billCursor.employee.toString(),
      booking: billCursor.booking ? billCursor.booking.toString() : null,
      tables: billCursor.tables ? billCursor.tables.map((table) => table.toString()) : null,
      order: billCursor.order.toString()
    };

    // check for existence
    const bookingCondition = { _id: bookingId, hotel: hotelId };
    const bookingCursor = await Booking.findOne(bookingCondition);
    if (!bookingCursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const bookingSpread = {
      ...bookingCursor._doc,
      _id: bookingCursor.id,
      hotel: bookingCursor.hotel.toString(),
      guest: bookingCursor.guest.toString(),
      plan: bookingCursor.plan.toString(),
      agent: bookingCursor.agent.toString()
    };

    let dueAmount = Number(parseFloat(bookingSpread.dueAmount).toFixed(2)) + Number(parseFloat(billSpread.billAmount).toFixed(2));
    dueAmount = Math.round(dueAmount);

    // update booking
    const updateBookingObject = await Booking.findByIdAndUpdate(bookingId,
      { dueAmount: dueAmount });
    if (!updateBookingObject) throw new customError(ERR_MSG.BILL_NOT_ATTACHED, ERR_CODE.INTERNAL);

    const updateBillObject = await Bill.findByIdAndUpdate(billId, { booking: bookingId, status: TABLE_STATUS.ATTACHED });
    if (!updateBillObject) throw new customError(ERR_MSG.BILL_NOT_SAVE, ERR_CODE.INTERNAL);

    if (billSpread.type === ORDER_TYPE.FOOD) {
      // update order
      await Order.findByIdAndUpdate(billSpread.order, { booking: bookingId, status: TABLE_STATUS.ATTACHED });

      // update table
      billSpread.tables.map(async (table) => {
        await Table.findByIdAndUpdate(table, { status: TABLE_STATUS.EMPTY });
      });
    }

    // find bill data
    const condition = { _id: billId, hotel: hotelId };
    const cursor = await Bill.findOne(condition);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employee: cursor.employee.toString(),
      booking: cursor.booking ? cursor.booking.toString() : null,
      tables: cursor.tables ? cursor.tables.map((table) => table.toString()) : null,
      order: cursor.order.toString()
    };

    return spread;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = {
  mongoGetAdvancePayment, mongoAddAdvancePayment, mongoModAdvancePayment, mongoDelAdvancePayment,
  mongoBillPayments, mongoSearchBillPayment, mongoGetBillPayment, mongoAddBillPayment,
  mongoModBillPayment, mongoDelBillPayment, mongoAttachBill
};