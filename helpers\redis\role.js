const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");


// Function to get role data from Redis
const getOneRoleRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_ROLE}_${hotelId}:${id}`);
        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all role data from Redis
const getAllRoleRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_ROLE}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(hashKeys.map(
            async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));
        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set role data in Redis
const setOneRoleRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneRoleRedis(hotelId, data._id);
            await addHashValues(`${process.env.HASH_ROLE}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    color: data.color,
                    permissions: JSON.stringify(data.permissions || []),
                    description: data.description,
                    isEnable: data.isEnable
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set role data in Redis
const setAllRoleRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllRoleRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_ROLE}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        color: data.color,
                        permissions: JSON.stringify(data.permissions || []),
                        description: data.description,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                );
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete single data from Redis
const delOneRoleRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_AGENT}_${hotelId}:${id}`)
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete role data from Redis
const delAllRoleRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_ROLE}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey);
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};


module.exports = {
    getOneRoleRedis, getAllRoleRedis, setOneRoleRedis,
    setAllRoleRedis, delOneRoleRedis, delAllRoleRedis
};