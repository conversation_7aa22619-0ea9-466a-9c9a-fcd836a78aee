const mongoose = require("mongoose");
const Schema = mongoose.Schema;


const employeeSchema = new Schema(
    {
        hotel: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: "Hotel"
        },
        role: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: "Role"
        },
        name: {
            type: String,
            required: true,
            trim: true,
            uppercase: true
        },
        address: {
            type: String,
            required: true,
            trim: true,
            uppercase: true
        },
        mobile: {
            type: String,
            required: true
        },
        email: {
            type: String,
            required: true,
            match: [/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/, 'Invalid email!']
        },
        password: {
            type: String,
            default: null
        },
        otp: {
            type: String,
            default: null
        },
        expirationTime: {
            type: Date,
            default: null
        },
        iat: {
            type: String,
            trim: true,
            default: null
        },
        isEnable: {
            type: Boolean,
            default: true,
            required: true
        }
    },
    { timestamps: true }
);


module.exports = mongoose.model("Employee", employeeSchema);