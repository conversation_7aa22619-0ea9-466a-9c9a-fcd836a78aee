const jwt = require("jsonwebtoken");
const Hotel = require("../models/hotel");
const Employee = require("../models/employee");
const customError = require('../helpers/customError');


//handle verify token
const verifyJWT = (req, res, next) => {
    req.isAuthenticated = false;

    try {
        const authHeader = req.headers.authorization || req.headers.Authorization;
        if (!authHeader) return next();
        if (!authHeader?.startsWith("Bearer ")) return next();

        const token = authHeader.split(" ")[1];

        jwt.verify(
            token,
            process.env.JWT_TOKEN_SECRET,
            async (error, decoded) => {
                if (error) {
                    if (error.name === 'TokenExpiredError' && req.cookies?.refreshToken) {
                        try {
                            const rd = jwt.verify(req.cookies.refreshToken, REFRESH_SECRET);
                            const { accessToken, refreshToken } = mintTokens(rd.UserInfo);
                            req.res.cookie('accessToken', accessToken, cookieOpts);
                            req.res.cookie('refreshToken', refreshToken, refreshOpts);
                            attach({ UserInfo: rd.UserInfo, iat: jwt.decode(accessToken).iat }); return next();
                        } catch {
                            return next();
                        }
                    }

                    // if (error.name === 'JsonWebTokenError') {
                    //     jwt.verify(
                    //         token,
                    //         process.env.JWT_REFRESH_TOKEN_SECRET,
                    //         async (errR, decodedR) => {
                    //             if (!errR) {
                    //                 const { hotelId, employeeId, employeeName, employeeRole } = decodedR.UserInfo;

                    //                 req.hotelId = hotelId;
                    //                 req.employeeId = employeeId;
                    //                 req.employeeName = employeeName;
                    //                 req.employeeRole = employeeRole;
                    //                 req.iat = decodedR.iat;
                    //                 req.isAuthenticated = true;
                    //             }
                    //         }
                    //     );
                    // }

                    return next();
                }

                const { hotelId, employeeId, employeeName, employeeRole } = decoded.UserInfo;

                if (hotelId) {
                    const findHotel = await Hotel.findOne({ _id: hotelId, isEnable: true });
                    if (!findHotel) return next();
                }

                const findEmployee = await Employee.findOne({ hotel: hotelId, _id: employeeId, iat: decoded.iat, isEnable: true });
                if (!findEmployee) return next();

                req.hotelId = hotelId;
                req.employeeId = employeeId;
                req.employeeName = employeeName;
                req.employeeRole = employeeRole;
                req.iat = decoded.iat;
                req.isAuthenticated = true;

                next();
            }
        );
    } catch (error) {
        next(new customError(error.message, error.code || 403));
    }
};


module.exports = verifyJWT;