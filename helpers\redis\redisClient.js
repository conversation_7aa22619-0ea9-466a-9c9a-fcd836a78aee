const Redis = require("ioredis");


// const indexExists = async (redis, indexName) => {
//     try {
//         const indexes = await redis.call('FT._LIST');
//         return indexes.includes(indexName);
//     } catch (err) {
//         console.error('Error checking index:', err);
//         return false;
//     }
// };

// const dropIndexIfExists = async (redis, indexName) => {
//     const exists = await indexExists(redis, indexName);
//     if (exists) {
//         await redis.call('FT.DROPINDEX', indexName); // 'DD' deletes documents too
//         console.log(`Dropped index: ${indexName}`);
//     }
// };

const redis = new Redis({
    host: process.env.DB_REDIS_HOST,
    port: process.env.DB_REDIS_PORT,
    password: process.env.DB_REDIS_PASSWORD
});

redis.on('connect', async () => {
    console.log("Redis server connected...");
});

redis.on('error', (error) => {
    console.error('Redis connection error:', error);
});


module.exports = redis;