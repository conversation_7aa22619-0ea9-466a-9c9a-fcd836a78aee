require('dotenv').config();
const os = require('os');
const express = require("express");
const bodyParser = require("body-parser");
const graphqlHttp = require("express-graphql").graphqlHTTP;
const cookieParser = require('cookie-parser');
// const https = require("https");
// const fs = require("fs");
// const mongoose = require("mongoose");
const verifyJWT = require("./middlewares/verifyJWT");
const graphQlResolvers = require("./graphql/resolver/index");
const graphQlSchema = require("./graphql/schema/schema");
const cron = require("node-cron");
const PopulateRedis = require("./helpers/redis/init");
const { backupDB } = require("./graphql/resolver/dbOperations");
const { stringToBoolean } = require("./helpers/boolean");
const { connectMongo, isMongoConnected, testMongoConnection } = require("./helpers/mongoClient");
const { initializeAllIndexes, searchHealthCheck } = require("./helpers/indexManager");

const APP_PORT = process.env.APP_PORT || process.env.APP_PORT_ALTERNATIVE;

// var privateKey = fs.readFileSync(process.env.APP_SSL_KEY_FILE, 'utf8');
// var certificate = fs.readFileSync(process.env.APP_SSL_CERT_FILE, 'utf8');
// var credentials = { key: privateKey, cert: certificate };

const app = express();
// const httpsServer = https.createServer(credentials, app);

app.use(bodyParser.json());

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const mongoStatus = isMongoConnected();
    const searchHealth = await searchHealthCheck();

    const healthStatus = {
      status: 'OK',
      timestamp: new Date().toISOString(),
      services: {
        mongodb: mongoStatus ? 'Connected' : 'Disconnected',
        redis: stringToBoolean(process.env.DB_REDIS_ENABLED) ? 'Enabled' : 'Disabled'
      },
      search: searchHealth
    };

    const isHealthy = mongoStatus && searchHealth.search_ready;
    res.status(isHealthy ? 200 : 503).json(healthStatus);
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      timestamp: new Date().toISOString(),
      error: error.message
    });
  }
});

app.use((req, res, next) => {
  const containerHostname = os.hostname();
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('X-Handled-By', containerHostname);

  if (req.method === 'OPTIONS') {
    return res.sendStatus(200);
  }

  next();
});

app.use(cookieParser());

// Global JWT auth Handling Middleware 
app.use(verifyJWT);

// Set up the /graphql endpoint with the express-graphql middleware
app.use("/graphql", async (req, res) => {
  graphqlHttp({
    schema: graphQlSchema,
    rootValue: graphQlResolvers,
    graphiql: true,
    customFormatErrorFn: (error) => {
      console.error("GraphQL Error:", error.message);

      return {
        message: error.message,
        code: error.originalError?.code || 500
      };
    }
  })(req, res);
});


// app.use('/graphql', graphqlHttp({
//   schema: graphQlSchema,
//   rootValue: graphQlResolvers,
//   graphiql: true
// }));

// Start the server
const startServer = async () => {
  try {
    // First, try to connect to MongoDB
    // console.log("Attempting to connect to MongoDB...");
    const mongoConnected = await connectMongo();

    if (mongoConnected) {
      console.log("✅ MongoDB connection successful");

      // Initialize search indexes
      const indexesInitialized = await initializeAllIndexes();
      if (indexesInitialized) {
      } else {
        console.error("⚠️  Some search indexes failed to initialize");
      }

      // Initialize Redis if enabled
      if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
        try {
          await PopulateRedis();
        } catch (error) {
          console.error("❌ Redis population failed:", error.message);
        }
      }
    } else {
      console.error("❌ MongoDB connection failed - Server will start but database operations may fail");
    }

    // Start the web server regardless of database connection
    app.listen(APP_PORT, () => {
      console.log(`🚀 Web server started on port: ${APP_PORT}`);
      console.log(`📊 MongoDB Status: ${mongoConnected ? 'Connected' : 'Disconnected'}`);
    });

  } catch (error) {
    console.error("❌ Server startup error:", error.message);
    process.exit(1);
  }
};

// Start the server
startServer();

// Graceful shutdown handling
process.on('SIGTERM', async () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  await gracefulShutdown();
});

process.on('SIGINT', async () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  await gracefulShutdown();
});

const gracefulShutdown = async () => {
  try {
    console.log('📊 Checking MongoDB connection status...');
    if (isMongoConnected()) {
      console.log('🔌 Closing MongoDB connection...');
      await require('mongoose').connection.close();
      console.log('✅ MongoDB connection closed');
    }

    console.log('👋 Server shutdown complete');
    process.exit(0);
  } catch (error) {
    console.error('❌ Error during shutdown:', error.message);
    process.exit(1);
  }
};

// populate redis data every 1 hour
cron.schedule(process.env.DB_REDIS_REPOPULATE_TIME, async () => {
  try {
    if (!isMongoConnected()) {
      console.log("⚠️  MongoDB not connected, attempting to reconnect...");
      const reconnected = await testMongoConnection();
      if (!reconnected) {
        console.error("❌ Failed to reconnect to MongoDB, skipping Redis population");
        return;
      }
    }

    if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      await PopulateRedis();
      console.log("✅ Redis population completed");
    }
  } catch (error) {
    console.error("❌ Error during Redis population:", error.message);
  }
});

// backup database mongo db every day at 12:00 AM
cron.schedule(process.env.APP_BACKUP_TIME, async () => {
  try {
    if (!isMongoConnected()) {
      console.log("⚠️  MongoDB not connected, attempting to reconnect...");
      const reconnected = await testMongoConnection();
      if (!reconnected) {
        console.error("❌ Failed to reconnect to MongoDB, skipping database backup");
        return;
      }
    }

    console.log("🔄 Starting database backup...");
    await backupDB();
    console.log("✅ Database backup completed");
  } catch (error) {
    console.error("❌ Error during database backup:", error.message);
  }
});