// const { max } = require("moment");
const mongoose = require("mongoose");
const Schema = mongoose.Schema;


const moduleSchema = new Schema({
    module: {
        type: String,
        required: true,
        min: 3,
        max: 20
    },
    operations: {
        type: [String],
        required: true,
        String
    }
});

const roleSchema = new Schema(
    {
        hotel: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: "Hotel"
        },
        name: {
            type: String,
            required: true,
            trim: true,
            uppercase: true
        },
        color: {
            type: String,
            default: "#fafafa",
            required: true
        },
        permissions: [moduleSchema],
        description: {
            type: String,
            trim: true
        },
        isEnable: {
            type: Boolean,
            default: true,
            required: true
        }
    },
    { timestamps: true }
);


module.exports = mongoose.model('Role', roleSchema);