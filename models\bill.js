const mongoose = require("mongoose");
require("mongoose-double")(mongoose);
const Schema = mongoose.Schema;
const { TABLE_STATUS } = require("../configs/tableOptions");


const billSchema = new Schema(
  {
    hotel: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Hotel"
    },
    employee: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "Employee"
    },
    booking: {
      type: Schema.Types.ObjectId,
      ref: "Booking"
    },
    tables: [
      {
        type: Schema.Types.ObjectId,
        ref: "Booking"
      }
    ],
    order: {
      type: Schema.Types.ObjectId,
      ref: "Order"
    },
    no: { type: Number, required: true, min: 0, default: 0 },
    type: { type: String, required: true },
    totalAmount: {
      type: Number,
      required: true,
      min: 0.01
    },
    cGSTPercentage: {
      type: Number
    },
    sGSTPercentage: {
      type: Number
    },
    cGSTAmount: {
      type: Number
    },
    sGSTAmount: {
      type: Number
    },
    serviceChargePercentage: {
      type: Number
    },
    serviceChargeAmount: {
      type: Number
    },
    billAmount: {
      type: Number,
      required: true,
      min: 0
    },
    status: {
      type: String,
      default: TABLE_STATUS.BILLED
    }
  },
  { timestamps: true }
);


module.exports = mongoose.model("Bill", billSchema);