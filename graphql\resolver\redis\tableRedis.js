const { Command } = require('ioredis');
const redis = require("../../../helpers/redis/redisClient");
const { decodeRedisVectorResults } = require("../../../helpers/redis/common");
const { setOneTableRedis } = require("../../../helpers/redis/table");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const FILE_NAME = "tableRedis.js";


// get all documents of a collection
const redisTables = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "redisTables";

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_TABLE_FILTER}`,
      query,
      'RETURN', '6', '_id', 'hotel', 'no', 'accommodation', 'description', 'status',
      'SORTBY', 'no', 'ASC',
      'LIMIT', '0', '30'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results;
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// search within collection
const redisSearchTable = async (hotelId, employeeId, searchKey) => {
  const FUNCTION_NAME = "redisSearchTable";
  const regex = new RegExp(searchKey, "i");

  try {
    // get all data
    const query = `(@hotel:{${hotelId}} @isEnable:{true})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_TABLE_FILTER}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'no', 'accommodation', 'description',
      'SORTBY', 'no', 'ASC',
      'LIMIT', '0', '30'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);

      if (!searchKey.trim()) {
        return results;
      }
      else {
        // filter array
        const filter = results.filter((item) => regex.test(item.no) ||
          regex.test(item.accommodation)) ||
          regex.test(item.description);

        return filter;
      }
    }

    return [];
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a single document by id from a collection
const redisGetTable = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisGetTable";

  try {
    // read single data
    const query = `(@hotel:{${hotelId}} @isEnable:{true} @_id:{${id}})`;
    const command = new Command('FT.SEARCH', [
      `${process.env.INDEX_TABLE_UNIQUE}`,
      query,
      'RETURN', '5', '_id', 'hotel', 'no', 'accommodation', 'description',
      'LIMIT', '0', '1'
    ]);
    const data = await redis.sendCommand(command);

    // Process the results
    if (data && data.length > 1) {
      const results = decodeRedisVectorResults(data);
      return results[0];
    }

    return {};
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisSetTable = async (hotelId, employeeId, data) => {
  const FUNCTION_NAME = "redisSetTable";

  try {
    // read single data
    await setOneTableRedis(hotelId, data);
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// Find a single document by id from a collection
const redisDelTable = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "redisDelTable";

  try {
    // delete single data
    const result = await redis.del(`${process.env.HASH_TABLE}_${hotelId}:${id}`);
    return result;
  } catch (error) {
    // record error in log
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { redisTables, redisSearchTable, redisGetTable, redisSetTable, redisDelTable };