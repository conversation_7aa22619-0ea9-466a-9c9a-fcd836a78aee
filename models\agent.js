const mongoose = require("mongoose");
const Schema = mongoose.Schema;


const agentSchema = new Schema(
    {
        hotel: {
            type: Schema.Types.ObjectId,
            required: true,
            ref: "Hotel"
        },
        name: {
            type: String,
            required: true,
            trim: true,
            uppercase: true,
        },
        description: {
            type: String,
            trim: true
        },
        isEnable: {
            type: Boolean,
            default: true,
            required: true
        },
        embedding: {
            type: [Number],
            default: []
        }
    },
    { timestamps: true }
);


module.exports = mongoose.model("Agent", agentSchema);