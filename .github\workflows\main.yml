name: Deploy

on:
  push:
    branches:
      - main

env:
  NETWORK_NAME: reverse_proxy
  REPO_URL: github.com/apradip/HotelApp-BackEnd
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  DOCKER_HOST: unix:///var/run/docker.sock

jobs:
  deploy:
    runs-on: self-hosted # Using self-hosted runner

    container:
      image: alpine:latest # Using Alpine container

    steps:
      - name: Install dependencies
        run: |
          apk add --no-cache git docker-cli python3 py3-pip build-base cython py3-setuptools curl docker-compose docker

      - name: Clone repository
        env:
          USERNAME_GITHUB: ${{ secrets.USERNAME_GITHUB }}
          PASSWORD_GITHUB: ${{ secrets.PASSWORD_GITHUB }}
        run: |
          echo "Cloning the repository..."
          rm -rf HotelApp-BackEnd || true
          git clone https://<EMAIL>/apradip/HotelApp-BackEnd.git

      - name: Clean up Docker environment
        run: |
          echo "Cleaning up old Docker containers and images..."
          docker rmi -f hotelapp-backend-api || true

      - name: Deploy using Docker Compose
        run: |
          cd HotelApp-BackEnd
          docker-compose down
          docker-compose up -d --force-recreate
          docker system prune -f
      - name: Add to Internal Network (nginx)
        run: |
          docker network connect reverse_proxy hotelapp_api
