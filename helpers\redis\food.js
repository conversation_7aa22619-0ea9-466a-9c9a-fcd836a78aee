const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");


// Function to get one food data from Redis
const getOneFoodRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_FOOD}_${hotelId}:${id}`);
        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all food data from Redis
const getAllFoodRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_FOOD}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(async (hashKey) => {
                return await readHashValues(hashKey);
            })
        );
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));
        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set food data in Redis
const setOneFoodRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneFoodRedis(hotelId, data._id);

            // const vectorBuffer = data.embedding ? Buffer.from(new Float32Array(data.embedding).buffer) : Buffer.from([]);
            await addHashValues(`${process.env.HASH_FOOD}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    name: data.name,
                    unitPrice: data.unitPrice,
                    description: data.description,
                    isEnable: data.isEnable,
                    // embedding: vectorBuffer,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set food data in Redis
const setAllFoodRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllFoodRedis(hotelId);

            dataArray.map(async (data) => {
                // const vectorBuffer = data.embedding ? Buffer.from(new Float32Array(data.embedding).buffer) : Buffer.from([]);
                await addHashValues(`${process.env.HASH_FOOD}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        name: data.name,
                        unitPrice: data.unitPrice,
                        description: data.description,
                        isEnable: data.isEnable,
                        // embedding: vectorBuffer,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                );
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete food data from Redis
const delOneFoodRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_FOOD}_${hotelId}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete food data from Redis
const delAllFoodRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_FOOD}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) => {
                    await delHashKey(hashKey);
                })
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};


module.exports = {
    getOneFoodRedis, getAllFoodRedis, setOneFoodRedis,
    setAllFoodRedis, delOneFoodRedis, delAllFoodRedis
};
