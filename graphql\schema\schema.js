const { buildSchema } = require("graphql");

module.exports = buildSchema(`
    type Auth {
        accessToken: String!
        refreshToken: String!
    }

    type CheckAuth {
        status: Boolean!
    }

    type SingleEmployee {
        mobile: String!
        email: String!
    }

    input LoginInput { 
        userName: String! 
        password: String! 
        hotelId: ID
    }

    input LoginOtpInput {
        userName: String!
        otp: String!
        hotelId: ID
    }

    input ChangePasswordInput {
        _id: ID!
        oldPassword: String!
        newPassword: String!
    }

    type Operation {
        operation: String!
        access: Boolean!
    }

    type Permission {
        module: String!
        operations: [Operation!]!
    }

    type Role {
        _id: ID!
        hotel: Hotel!
        name: String!
        color: String!
        permissions: [Permission!]!
        description: String
    }

    type ModulePermission {
        module: String!
        operations: [String!]!
    }

    input ModulePermissionInput {
        module: String!
        operations: [String!]!
    }

    input RoleInput {
        _id: ID
        name: String!
        color: String!
        permissions: [ModulePermissionInput!]!,
        description: String
    }

    input RolesInput {
        _ids: [ID!]!
    }

    type PaymentMode {
        _id: ID!
        hotel: Hotel!
        name: String!
        description: String
    }

    input PaymentModeInput {
        _id: ID
        name: String
        description: String
    }
    
    input PaymentModesInput {
        _ids: [ID!]!
    }

    type Agent {
        _id: ID!
        hotel: Hotel!
        name: String!
        description: String
    }

    input AgentInput {
        _id: ID
        name: String
        description: String
    }

    input AgentsInput {
        _ids: [ID!]!
    }

    type Identification {
        _id: ID!
        hotel: Hotel!
        name: String!
        description: String
    }

    input IdentificationInput {
        _id: ID
        name: String
        description: String
    }

    input IdentificationsInput {
        _ids: [ID!]!
    }

    type GST {
        _id: ID
        minTariff: Float
        maxTariff: Float
        cGSTPercentage: Float
        sGSTPercentage: Float
    }

    input GSTLimitInput {
        minTariff: String
        maxTariff: String
    }

    input GSTInput {
        _id: ID
        minTariff: Float!
        maxTariff: Float!
        cGSTPercentage: Float!
        sGSTPercentage: Float!
    }

    type Hotel {
        _id: ID!
        name: String!
        address: String!
        city: String!
        state: String!
        pin: String!
        phone: String
        email: String!
        webSiteUrl: String
        gstNo: String!
        urlName: String!
        urlPlace: String!
        foodCGSTPercentage: Float!
        foodSGSTPercentage: Float!
        serviceCGSTPercentage: Float!
        serviceSGSTPercentage: Float!
        serviceChargePercentage: Float!
        lastFinalBillNo: Int!
        lastFoodBillNo: Int!
        lastServiceBillNo: Int!
        lastMiscellaneousBillNo: Int!
        lastReceiptNo: Int!
        employees: [Employee]
    }

    input HotelInput {
        _id: ID
        name: String
        address: String
        city: String
        state: String
        pin: String
        phone: String
        email: String
        webSiteUrl: String
        gstNo: String
        urlName: String
        urlPlace: String
        foodCGSTPercentage: Float
        foodSGSTPercentage: Float
        serviceCGSTPercentage: Float
        serviceSGSTPercentage: Float
        serviceChargePercentage: Float
        lastKOTNo: Int
        lastSOTNo: Int
        lastMOTNo: Int
        lastFinalBillNo: Int
        lastFoodBillNo: Int
        lastServiceBillNo: Int
        lastMiscellaneousBillNo: Int
        lastReceiptNo: Int
    }

    input HotelsInput {
        _ids: [ID!]!
    }

    input URLHotelInput {
        urlName: String!
        urlPlace: String!
    }

    type Employee {
        _id: ID!
        hotel: Hotel!
        role: Role!
        name: String!
        address: String!
        mobile: String!
        email: String!
        password: String
        otp: String
        expirationTime: String
        refreshToken: String
    }

    input EmployeeInput {
        _id: ID
        role: ID!
        name: String!
        address: String
        mobile: String!
        email: String!
    }

    input EmployeesInput {
        _ids: [ID!]!
    }

    type Plan {
        _id: ID!
        hotel: Hotel!
        name: String!
        description: String
    }

    input PlanInput {
        _id: ID
        name: String
        description: String
    }

    input PlansInput {
        _ids: [ID!]!
    }

    type Food {
        _id: ID!
        hotel: Hotel!
        name: String!
        unitPrice: Float!
        description: String
    }

    input FoodInput {
        _id: ID
        name: String
        unitPrice: Float
        description: String
    }

    input FoodsInput {
        _ids: [ID!]!
    }

    type Service {
        _id: ID!
        hotel: Hotel!
        name: String!
        unitPrice: Float!
        description: String
    }

    input ServiceInput {
        _id: ID
        name: String
        unitPrice: Float
        description: String
    }

    input ServicesInput {
        _ids: [ID!]!
    }

    type Miscellaneous {
        _id: ID!
        hotel: Hotel!
        name: String!
        unitPrice: Float!
        description: String
    }

    input MiscellaneousInput {
        _id: ID
        name: String
        unitPrice: Float
        description: String
    }

    input MiscellaneaInput {
        _ids: [ID!]!
    }

    type RoomCategory {
        _id: ID!
        hotel: Hotel!
        name: String!
        accommodation: Int!
        tariff: Float!
        maxDiscount: Float!
        extraBedTariff: Float!
        extraPersonTariff: Float!
        description: String
    }

    input RoomCategoryInput {
        _id: ID
        name: String
        accommodation: Int
        tariff: Float
        maxDiscount: Float
        extraBedTariff: Float
        extraPersonTariff: Float
        description: String
    }

    input RoomCategoriesInput {
        _ids: [ID!]!
    }

    type Room {
        _id: ID!
        hotel: Hotel!
        category: RoomCategory!
        no: String!
        accommodation: Int!
        tariff: Float!
        extraBedTariff: Float!
        extraPersonTariff: Float!
        maxDiscount: Float!
        status: String!
    }

    input RoomInput {
        _id: ID
        category: ID!
        no: String!
        accommodation: Int!
        tariff: Float!
        extraBedTariff: Float!
        extraPersonTariff: Float!
        maxDiscount: Float!
    }

    input RoomsInput {
        _ids: [ID!]!
    }

    type Table {
        _id: ID!
        hotel: Hotel!
        no: String!
        accommodation: Float!
        description: String
        status: String
    }

    input TableInput {
        _id: ID
        no: String
        accommodation: Float
        description: String
    }

    input TablesInput {
        _ids: [ID!]!
    }

    type Guest {
        _id: ID!
        hotel: Hotel!
        employee: Employee!
        name: String!
        address: String
        city: String
        policeStation: String
        state: String
        pin: String
        mobile: String!
        email: String!
        father: String
        age: Int
        guestCount: Int!
        maleCount: Int!
        femaleCount: Int!
        childCount: Int!
        identification: Identification
        idNo: String
        company: String
        companyAddress: String
        companyGstNo: String 
    }

    input GuestInput {
        _id: ID
        name: String!
        address: String!
        city: String!
        policeStation: String!
        state: String!
        pin: String!
        mobile: String
        email: String
        father: String!
        age: Int!
        guestCount: Int!
        maleCount: Int!
        femaleCount: Int!
        childCount: Int!
        identification: ID!
        idNo: String!
        company: String
        companyAddress: String
        companyGstNo: String 
    }

    type BookedRoomItem {
        room: Room!
        guestCount: Int!
        extraBedCount: Int!
        extraPersonCount: Int!
        discount: Float!
        tariff: Float!
        cGSTPercentage: Float!
        cGSTAmount: Float!
        sGSTPercentage: Float!
        sGSTAmount: Float!
        occupancyDate: String!
        actualAccommodation: Int!
        actualTariff: Float!
        actualExtraBedTariff: Float!
        actualExtraPersonTariff: Float!
        actualMaxDiscount: Float!
        breakfastGuestCount: Int!
    }

    type BillItem {
        bill: ID!
        employee: ID!
        type: String!
        date: String!
        amount: Float!
    }

    type Booking {
        _id: ID!
        hotel: Hotel!
        employees: [Employee!]!
        guest: Guest!
        plan: Plan!
        agent: Agent!
        roomNos: String!
        startDate: String!
        endDate: String!
        dueAmount: Float!
        billNo: String
        status: String!
        rooms: [BookedRoomItem]
    }

    input BookingSearchInput {
        startDate: String
        endDate: String
    }

    input BookRoomInput {
        room: ID!
        no: String!
        guestCount: Int!
        extraPersonCount: Int!
        extraBedCount: Int!
        discount: Float!
        tariff: Float!
        occupancyDate: String!
        actualAccommodation: Int!
        actualTariff: Float!
        actualExtraPersonTariff: Float!
        actualExtraBedTariff: Float!
        actualMaxDiscount: Float!
    }

    input BookingInput {
        _id: ID
        guestId: ID
        guestName: String!
        guestAddress: String!
        guestCity: String!
        guestPoliceStation: String!
        guestState: String!
        guestPin: String!
        guestMobile: String
        guestEmail: String
        guestFather: String!
        guestAge: Int!
        guestCount: Int!
        guestMaleCount: Int!
        guestFemaleCount: Int!
        guestChildCount: Int!
        guestIdentification: ID!
        guestIdNo: String!
        company: String
        companyAddress: String
        companyGstNo: String 
        plan: ID!
        agent: ID!
        rooms: [BookRoomInput!]!
    }

    type CategoryItem {
        category: RoomCategory!
        roomCount: Int!
        tariff: Float!
        occupancyDate: String!
    }

    type AdvanceBookedGuest {
        _id: ID!
        hotel: Hotel!
        employee: Employee!
        name: String!
        address: String
        city: String
        policeStation: String
        state: String
        pin: String
        mobile: String!
        email: String!
        father: String
        age: Int
        guestCount: Int!
        maleCount: Int!
        femaleCount: Int!
        childCount: Int!
        identification: Identification
        idNo: String
        company: String
        companyAddress: String
        companyGstNo: String 
    }

    type AdvanceBooking {
        _id: ID!
        hotel: Hotel!
        employees: [Employee!]!
        guest: AdvanceBookedGuest!
        plan: Plan!
        agent: Agent!
        roomNos: String!
        startDate: String!
        endDate: String!
        rooms: [BookedRoomItem]
        dueAmount: Float!
        billNo: Int
        status: String!
    }

    input AdvanceBookingSearchInput {
        startDate: String
        endDate: String
    }

    input CategoryInput {
        category: ID!
        name: String!
        roomCount: Int!
        tariff: Float!
        occupancyDate: String!
    }

    input AdvanceBookingInput {
        _id: ID
        guestName: String!
        guestMobile: String!
        guestEmail: String
        guestCount: Int!
        company: String
        companyAddress: String
        companyGstNo: String
        plan: ID!
        agent: ID!
        categories: [CategoryInput!]!
    }

    type OrderItem {
        _id: ID!
        itemId: String!
        name: String!
        unitPrice: Float!
        quantity: Int!
        cGSTPercentage: Float
        sGSTPercentage: Float
        serviceChargePercentage: Float
        cGSTAmount: Float
        sGSTAmount: Float
        serviceChargeAmount: Float
        status: String!
    }

    type TokenItem {
        _id: ID!
        tokenNo: Int!
        employee: Employee
        deliveryDate: String
        deliveryTime: String
        items: [OrderItem!]!
        status: String!
        createdAt: String!
        updatedAt: String!
    }

    type Order {
        _id: ID!
        hotel: Hotel!
        employees: [Employee!]
        booking: Booking
        tables: [Table]
        type: String!
        token: TokenItem!
        status: String!
        createdAt: String!
        updatedAt: String!
    }

    type Order1 {
        _id: ID!
        hotel: Hotel!
        employees: [Employee!]!
        booking: Booking
        tables: [Table]
        type: String!
        token: TokenItem!
        status: String!
        createdAt: String!
        updatedAt: String!
    }
    
    input OrdersInput {
        type: String
        status: String
    }

    input OrderItemInput {
        _id: ID!
        quantity: Int!
        status: String!
    }

    input OrderInput {
        _id: ID
        type: String!
        booking: ID
        tables: [ID]
        tokenId : ID
        employee: ID
        deliveryDate: String
        deliveryTime: String
        items: [OrderItemInput!]!
    }

    input UpdateOrderStatusInput {
        _id: ID!
        tokenId: ID!
        orderItemIds: [ID!]!
        status: String!
    }

    input OrderSearchInput {
        type: String
        status: String
        startDate: String
        endDate: String
    }

    input TokenInput {
        _id: ID!
        tokenId: ID!
    }

    type Breakfast {
        _id: ID!
        hotel: Hotel!
        employees: [Employee!]!
        guest: Guest!
        plan: Plan!
        agent: Agent!
        roomNos: String!
        startDate: String!
        endDate: String!
        dueAmount: Float!
        billNo: Int
        status: String!
        rooms: [BookedRoomItem]
        breakfastDate: String!
        guestCount: Int!
        breakfastGuestCount: Int!
    }

    input BreakfastInput {
        _id: ID!
        guestCount: Int!
    }
    
    type Ledger {
        _id: ID!
        hotel: Hotel!
        employee: Employee!
        guest: Guest!
        plan: Plan!
        agent: Agent!
        roomNos: String!
        startDate: String!
        endDate: String!
        billNo: Int!
        rooms: [BookedRoomItem]
        orders: [Order!]!
        status: String!
        createdAt: String!
        updatedAt: String!
    }

    input BillInput {
        _id: ID!
        type: String
        attach: Boolean
    }

    input BillSearchInput {
        no: String
        startDate: String
        endDate: String
    }

    type Bill {
        _id: ID!
        hotel: Hotel!
        employee: Employee!
        booking: Booking
        tables: [Table]
        order: [Order!]!
        no: String!
        type: String!
        totalAmount: Float!
        cGSTPercentage: Float
        cGSTAmount: Float
        sGSTPercentage: Float
        sGSTAmount: Float
        serviceChargePercentage: Float
        serviceChargeAmount: Float!
        billAmount: Float!
        status: String!
        createdAt: String!
        updatedAt: String!
    }

    input PaymentInput {
        _id: ID!
        amount: Float!
        mode: ID!
        particular: String
    }
    
    input PaymentSearchInput {
        startDate: String
        endDate: String
    }

    type BillPayment {
        _id: ID!
        hotel: Hotel!
        employee: Employee!
        receiptNo: String!
        amount: Float!
        particular: String
        mode: PaymentMode!
        bill: Bill
        booking: Booking
        createdAt: String!
        updatedAt: String!
    }

    type AdvancePayment {
        _id: ID!
        hotel: Hotel!
        employee: Employee!
        mode: PaymentMode!
        receiptNo: Int!
        date: String!
        amount: Float!
        particular: String
    }

    input BillAttachInput {
        _id: ID!
        bookingId: ID!
    }

    input RoomSalesSearchInput {
        startDate: String
        endDate: String
    }

    type RoomSales {
        occupancyDate: String!
        room: String!
        tariff: Float!
    }

    type Checkout {
        _id: ID!
        hotel: Hotel!
        employee: Employee
        guest: Guest!
        plan: Plan!
        agent: Agent!
        roomNos: String!
        startDate: String!
        endDate: String!
        rooms: [BookedRoomItem]
        dueAmount: Float!
        createdAt: String!
        updatedAt: String!
    }

    input ActivityLogsInput {
        _ids: [ID!]!
    }

    type LogFile {
        fileName: String!
        fileSize: String!
        fileDate: String!
    }

    type ActivityLog {
        dated: String!
        hotel: String!
        employee: String!
        activity: String!
    }

    type ErrorLog {
        dated: String!
        hotel: String!
        employee: String!
        module: String!
        process: String!
        message: String
    }

    input LogInput {
        fileName: String!
    }

    input LogsInput {
        fileNames: [String!]
    }

    input RestoreInput {
        fileName: String!
    }

    type RootQuery {
        checkAuth: CheckAuth!    
        findEmployee(employeeSearchInput: String!): SingleEmployee! 
        roles: [Role!]!
        searchRole(roleSearchInput: String!): [Role!]!
        getRole(_id: ID!): Role!
        roomCategories: [RoomCategory!]!
        searchRoomCategory(roomCategorySearchInput: String!): [RoomCategory!]!
        getRoomCategory(_id: ID!): RoomCategory!
        paymentModes: [PaymentMode!]!
        searchPaymentMode(paymentModeSearchInput: String!): [PaymentMode!]!
        getPaymentMode(_id: ID!): PaymentMode!
        agents: [Agent!]!
        searchAgent(agentSearchInput: String!): [Agent!]!
        getAgent(_id: ID!): Agent!
        identifications: [Identification!]!
        searchIdentification(identificationSearchInput: String!): [Identification!]!
        getIdentification(_id: ID!): Identification!
        gsts: [GST!]!
        searchGST(gstSearchInput: String!): [GST!]!
        getGST(_id: ID!): [GST!]!
        hotels: [Hotel!]!
        searchHotel(hotelSearchInput: String!): [Hotel!]!
        getHotel(_id: ID!): Hotel!
        getHotelFromUrl(urlHotelInput: URLHotelInput!): Hotel!
        employees: [Employee!]!
        searchEmployee(employeeSearchInput: String!): [Employee!]!
        getEmployee(_id: ID!): Employee!
        plans: [Plan!]!
        searchPlan(planSearchInput: String!): [Plan!]!
        getPlan(_id: ID!): Plan!
        foods: [Food!]!
        searchFood(foodSearchInput: String!): [Food!]!
        getFood(_id: ID!): Food!
        services: [Service!]!
        searchService(serviceSearchInput: String!): [Service!]!
        getService(_id: ID!): Service!
        miscellanea: [Miscellaneous!]!
        searchMiscellaneous(miscellaneousSearchInput: String!): [Miscellaneous!]!
        getMiscellaneous(_id: ID!): Miscellaneous!
        rooms: [Room!]!
        searchRoom(roomSearchInput: String!): [Room!]!
        getRoom(_id: ID!): Room!        
        tables: [Table!]!
        searchTable(tableSearchInput: String!): [Table!]!
        getTable(_id: ID!): Table!        
        guests: [Guest!]!
        searchGuest(guestSearchInput: String!): [Guest!]!
        getGuest(_id: ID!): Guest!        
        advanceBookings: [AdvanceBooking!]!
        searchAdvanceBooking(advanceBookingSearchInput: AdvanceBookingSearchInput!): [AdvanceBooking!]!
        getAdvanceBooking(_id: ID!): AdvanceBooking!    
        bookings: [Booking!]!
        searchBooking(bookingSearchInput: BookingSearchInput!): [Booking!]!
        getBooking(_id: ID!): Booking!    
        oldBookings: [Booking!]!
        searchOldBooking(bookingSearchInput: BookingSearchInput!): [Booking!]!
        orders(ordersInput: OrdersInput!): [Order!]!
        searchOrder(orderSearchInput: OrderSearchInput!): [Order!]!
        getOrder(tokenInput: TokenInput!): [Order!]!    
        breakfasts: [Breakfast!]
        getBreakfast(_id: ID!): Breakfast!
        bills: [Bill!]!
        finalBills(billSearchInput: BillSearchInput!): [Ledger!]!
        searchBill(billSearchInput: BillSearchInput!): [Bill!]!
        getBill(_id: ID!): Bill!   
        getLedger(_id: ID!): Ledger!   
        billPayments: [BillPayment!]!
        searchBillPayment(paymentSearchInput: PaymentSearchInput!): [BillPayment!]!
        getBillPayment(_id: ID!): BillPayment!        
        getAdvancePayment(_id: ID!): [AdvancePayment!]!
        roomSales(roomSalesSearchInput: RoomSalesSearchInput!): [RoomSales!]    
        getCheckout(dateInput: String!): [Checkout!]
        activityLogFiles: [LogFile!]
        activityLogDetail(logInput: LogInput!): [ActivityLog!]
        errorLogFiles: [LogFile!]
        errorLogDetail(logInput: LogInput!): [ErrorLog!]
    }
    
    type RootMutation {
        forgetPassword(forgetPasswordInput: String!): SingleEmployee!
        login(loginInput: LoginInput): Auth!
        loginOtp(loginOtpInput: LoginOtpInput): Auth!
        changePassword(changePasswordInput: ChangePasswordInput!): SingleEmployee!
        logout(logoutInput: String!): SingleEmployee!
        modRole(roleInput: RoleInput!): Role!
        addRoomCategory(roomCategoryInput: RoomCategoryInput!): RoomCategory!
        modRoomCategory(roomCategoryInput: RoomCategoryInput!): RoomCategory!
        delRoomCategory(_id: ID!): RoomCategory!
        delRoomCategories(roomCategoriesInput: RoomCategoriesInput!): [RoomCategory!]!
        addPaymentMode(paymentModeInput: PaymentModeInput!): PaymentMode!
        modPaymentMode(paymentModeInput: PaymentModeInput!): PaymentMode!
        delPaymentMode(_id: ID!): PaymentMode!
        delPaymentModes(paymentModesInput: PaymentModesInput!): [PaymentMode!]!
        addAgent(agentInput: AgentInput!): Agent!
        modAgent(agentInput: AgentInput!): Agent!
        delAgent(_id: ID!): Agent!
        delAgents(agentsInput: AgentsInput!): [Agent!]!
        addIdentification(identificationInput: IdentificationInput!): Identification!
        modIdentification(identificationInput: IdentificationInput!): Identification!
        delIdentification(_id: ID!): Agent!
        delIdentifications(identificationsInput: IdentificationsInput!): [Identification!]!
        addGST(gstInput: GSTInput!): GST!
        modGST(gstInput: GSTInput!): GST!
        delGST(_id: ID!): GST!
        addHotel(hotelInput: HotelInput!): Hotel!    
        modHotel(hotelInput: HotelInput!): Hotel!
        delHotel(_id: ID!): Hotel!
        delHotels(hotelsInput: HotelsInput!): [Hotel!]!
        addEmployee(employeeInput: EmployeeInput!): Employee!    
        modEmployee(employeeInput: EmployeeInput!): Employee!
        delEmployee(_id: ID!): Employee!
        delEmployees(employeesInput: EmployeesInput!): [Employee!]!
        addPlan(planInput: PlanInput!): Plan!    
        modPlan(planInput: PlanInput!): Plan!
        delPlan(_id: ID!): Plan!
        delPlans(plansInput: PlansInput!): [Plan!]!
        addFood(foodInput: FoodInput!): Food!    
        modFood(foodInput: FoodInput!): Food!
        delFood(_id: ID!): Food!
        delFoods(foodsInput: FoodsInput!): [Food!]!
        addService(serviceInput: ServiceInput!): Service!    
        modService(serviceInput: ServiceInput!): Service!
        delService(_id: ID!): Service!
        delServices(servicesInput: ServicesInput!): [Service!]!
        addMiscellaneous(miscellaneousInput: MiscellaneousInput!): Miscellaneous!    
        modMiscellaneous(miscellaneousInput: MiscellaneousInput!): Miscellaneous!
        delMiscellaneous(_id: ID!): Miscellaneous!
        delMiscellanea(miscellaneaInput: MiscellaneaInput!): [Miscellaneous!]!
        addRoom(roomInput: RoomInput!): Room!    
        modRoom(roomInput: RoomInput!): Room!
        delRoom(_id: ID!): Room!
        delRooms(roomsInput: RoomsInput!): [Room!]!
        addTable(tableInput: TableInput!): Table!    
        modTable(tableInput: TableInput!): Table!
        delTable(_id: ID!): Table!
        delTables(tablesInput: TablesInput!): [Table!]!
        addGuest(guestInput: GuestInput!): Guest!    
        modGuest(guestInput: GuestInput!): Guest!
        delGuest(_id: ID!): Guest!
        addAdvanceBooking(advanceBookingInput: AdvanceBookingInput!): AdvanceBooking!    
        modAdvanceBooking(advanceBookingInput: AdvanceBookingInput!): AdvanceBooking!
        delAdvanceBooking(_id: ID!): AdvanceBooking!
        addBooking(bookingInput: BookingInput!): Booking!    
        modBooking(bookingInput: BookingInput!): Booking!
        delBooking(_id: ID!): Booking!
        addOrder(orderInput: OrderInput!): [Order!]!
        modOrder(orderInput: OrderInput!): [Order!]!
        delOrder(tokenInput: TokenInput!): [Order!]!
        modOrderStatus(updateOrderStatusInput: UpdateOrderStatusInput!): [Order!]
        modBreakfast(breakfastInput: BreakfastInput!): Breakfast!
        generateBill(billInput: BillInput!): Bill!
        reGenerateBill(_id: ID!): Bill!
        delBill(_id: ID!): Bill!
        addAdvancePayment(paymentInput: PaymentInput!): AdvancePayment!
        modAdvancePayment(paymentInput: PaymentInput!): AdvancePayment!
        delAdvancePayment(_id: ID!): AdvancePayment!
        addBillPayment(paymentInput: PaymentInput!): BillPayment!    
        modBillPayment(paymentInput: PaymentInput!): BillPayment!    
        attachBill(billAttachInput: BillAttachInput!): Bill!
        delBillPayment(_id: ID!): BillPayment!
        addCheckout(_id: ID!): Booking!
        yearEnd: Boolean!
        delActivityLogFiles(logsInput: LogsInput!): [LogFile!]!
        delErrorLogFiles(logsInput: LogsInput!): [LogFile!]
        backupDB: Boolean!
        restoreDB(restoreInput: RestoreInput): Boolean! 
        importDB: Boolean!
        updateDataWithAI: Boolean!
    }

    schema {
        query: RootQuery
        mutation: RootMutation
    }
`);