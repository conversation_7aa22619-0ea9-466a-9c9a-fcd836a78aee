const { addDays } = require('date-fns');
const moment = require('moment-timezone');


function stringToDbDate(parameterDate) {
  const parsedDate = moment(parameterDate, "DD/MM/YYYY");
  const istDate = parsedDate.tz("Asia/Kolkata").format("YYYY-MM-DD");
  const increasedDate = addDays(new Date(istDate), 1);

  return increasedDate;
}

function frontEndToDbDate(parameterDate) {
  const parsedDate = moment(parameterDate, "DD/MM/YYYY");
  const istDate = parsedDate.tz("Asia/Kolkata").format("YYYY-MM-DD");

  return istDate;
}

function dateToString(parameterDateTime) {
  const mongoDate = new Date(parameterDateTime);
  const istFormatDateTime = moment(mongoDate).tz("Asia/Kolkata").format("DD/MM/YYYY HH:mm:ss");

  return istFormatDateTime;
}

function isSameDay(d1, d2) {
  return d1.getFullYear() === d2.getFullYear() &&
    d1.getMonth() === d2.getMonth() &&
    d1.getDate() === d2.getDate();
}


module.exports = { stringToDbDate, frontEndToDbDate, dateToString, isSameDay };
