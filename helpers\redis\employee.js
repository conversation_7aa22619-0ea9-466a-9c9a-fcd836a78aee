const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");


// Function to get one employee data from Redis
const getOneEmployeeRedis = async (hotelId, id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_EMPLOYEE}_${hotelId}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all employee data from Redis
const getAllEmployeeRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_EMPLOYEE}_${hotelId}`;
        const pattern = '*';
        const hashKeys = await searchHashKeys(prefix, pattern);
        const parsedData = await Promise.all(
            hashKeys.map(async (hashKey) => {
                return await readHashValues(hashKey);
            }));
        const sortedByName = parsedData.sort((a, b) => a.name.localeCompare(b.name));

        return sortedByName;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set employee data in Redis
const setOneEmployeeRedis = async (hotelId, data) => {
    try {
        if (data) {
            await delOneEmployeeRedis(hotelId, data._id);

            await addHashValues(`${process.env.HASH_EMPLOYEE}_${hotelId}:${data._id}`,
                {
                    _id: data._id,
                    hotel: data.hotel,
                    role: data.role,
                    name: data.name,
                    address: data.address,
                    mobile: data.mobile,
                    email: data.email,
                    password: data.password,
                    otp: data.otp,
                    expirationTime: data.expirationTime,
                    iat: data.iat,
                    isEnable: data.isEnable,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set employee data in Redis
const setAllEmployeeRedis = async (hotelId, dataArray) => {
    try {
        if (dataArray) {
            await delAllEmployeeRedis(hotelId);

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_EMPLOYEE}_${hotelId}:${data._id}`,
                    {
                        _id: data._id,
                        hotel: data.hotel,
                        role: data.role,
                        name: data.name,
                        address: data.address,
                        mobile: data.mobile,
                        email: data.email,
                        password: data.password,
                        otp: data.otp,
                        expirationTime: data.expirationTime,
                        iat: data.iat,
                        isEnable: data.isEnable,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                )
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete employee data from Redis
const delOneEmployeeRedis = async (hotelId, id) => {
    try {
        await delHashKey(`${process.env.HASH_EMPLOYEE}_${hotelId}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete employee data from Redis
const delAllEmployeeRedis = async (hotelId) => {
    try {
        const prefix = `${process.env.HASH_EMPLOYEE}_${hotelId}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) =>
                    await delHashKey(hashKey)
                )
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};


module.exports = {
    getOneEmployeeRedis, getAllEmployeeRedis, setOneEmployeeRedis,
    setAllEmployeeRedis, delOneEmployeeRedis, delAllEmployeeRedis
};
