{"name": "HotelApp-API", "version": "2.1.3", "description": "", "main": "index.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest --watchAll"}, "repository": {"type": "git", "url": "/apradip/hotelapp/backend"}, "keywords": ["hotelApp", "rest api", "graphql"], "author": "pixel", "license": "ISC", "dependencies": {"@huggingface/inference": "^4.4.0", "@langchain/anthropic": "^0.3.23", "@langchain/langgraph": "^0.3.5", "@langchain/langgraph-checkpoint-mongodb": "^0.0.6", "@langchain/mongodb": "^0.1.0", "@node-redis/json": "^1.0.2", "adm-zip": "^0.5.16", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "body-parser": "^2.2.0", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "express": "^5.1.0", "express-graphql": "^0.12.0", "express-validator": "^7.2.1", "fs": "0.0.2", "graphql": "^15.10.1", "https": "^1.0.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.29", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mongodb": "^6.17.0", "mongoose": "^8.15.2", "mongoose-double": "^0.0.1", "mysql": "^2.18.1", "mysql2": "^3.14.1", "node-cron": "^4.1.0", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3"}, "devDependencies": {"nodemon": "^3.1.10"}}