const { searchHashKeys, readHashValues, addHashValues, delHashKey } = require("./common");
const { ERR_CODE } = require("../../configs/messageOptions");


// Function to get GST data from Redis
const getOneGSTRedis = async (id) => {
    try {
        const data = await readHashValues(`${process.env.HASH_GST}:${id}`);

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to get all GST data from Redis
const getAllGSTRedis = async () => {
    try {
        const prefix = `${process.env.HASH_GST}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);
        const data = await Promise.all(
            hashKeys.map(async (hashKey) =>
                await readHashValues(hashKey))
        );

        return data;
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set gst data in Redis
const setOneGSTRedis = async (data) => {
    try {
        if (data) {
            await delOneGSTRedis(data._id);

            await addHashValues(`${process.env.HASH_GST}:${data._id}`,
                {
                    _id: data._id,
                    minTariff: data.minTariff,
                    maxTariff: data.maxTariff,
                    cGSTPercentage: data.cGSTPercentage,
                    sGSTPercentage: data.sGSTPercentage,
                    createdAt: data.createdAt,
                    updatedAt: data.updatedAt
                }
            );
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to set GST data in Redis
const setAllGSTRedis = async (dataArray) => {
    try {
        if (dataArray) {
            await delAllGSTRedis();

            dataArray.map(async (data) => {
                await addHashValues(`${process.env.HASH_GST}:${data._id}`,
                    {
                        _id: data._id,
                        minTariff: data.minTariff,
                        maxTariff: data.maxTariff,
                        cGSTPercentage: data.cGSTPercentage,
                        sGSTPercentage: data.sGSTPercentage,
                        createdAt: data.createdAt,
                        updatedAt: data.updatedAt
                    }
                )
            });
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete gst data from Redis
const delOneGSTRedis = async (id) => {
    try {
        await delHashKey(`${process.env.HASH_GST}:${id}`);
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};

// Function to delete GST data from Redis
const delAllGSTRedis = async () => {
    try {
        const prefix = `${process.env.HASH_GST}`;
        const pattern = `*`;
        const hashKeys = await searchHashKeys(prefix, pattern);

        if (hashKeys) {
            await Promise.all(
                hashKeys.map(async (hashKey) =>
                    await delHashKey(hashKey)));
        }
    } catch (error) {
        throw new Error(error.message, ERR_CODE.INTERNAL);
    }
};


module.exports = {
    getOneGSTRedis, getAllGSTRedis, setOneGSTRedis,
    setAllGSTRedis, delOneGSTRedis, delAllGSTRedis
};