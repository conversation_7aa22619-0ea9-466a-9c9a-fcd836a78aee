const Booking = require("../../../models/booking");
const Plan = require("../../../models/plan");
const { getAllPlanRedis } = require("../../../helpers/redis/plan");
const { stringToBoolean } = require("../../../helpers/boolean");
const { writeErrLog } = require("../../../helpers/log");
const { customError } = require('../../../helpers/customError');
const { ROOM_STATUS } = require("../../../configs/roomOptions");
const { ERR_CODE, ERR_MSG } = require("../../../configs/messageOptions");
const FILE_NAME = "breakfastMongo.js";


// get all breakfast collection
const mongoBreakfasts = async (hotelId, employeeId) => {
  const FUNCTION_NAME = "mongoBreakfasts";

  try {
    const data = [];
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    const currentDay = currentDate.getDate();

    // get plan data
    let planSpread = null;
    if (!stringToBoolean(process.env.DB_REDIS_ENABLED)) {
      const condition = { hotel: hotelId, isEnable: true };
      const order = { name: 1 };
      const cursor = await Plan.find(condition).sort(order);
      planSpread = cursor.map((object) => {
        return { ...object._doc, _id: object.id, hotel: object.hotel.toString() };
      });
    }
    else {
      planSpread = await getAllPlanRedis(hotelId);
    }

    const searchArray = planSpread.filter((item) => item.description && item.description.includes("Breakfast"));
    const planIds = searchArray.map(plan => plan._id.toString()).join(',');
    planSpread = planIds.split(',').map(id => id.trim());

    const condition = {
      hotel: hotelId,
      plan: { $in: planSpread },
      status: { $nin: [ROOM_STATUS.CHECKED_OUT, ROOM_STATUS.ADVANCE_BOOKED] }
    };
    const order = { startDate: 1, roomNos: 1 };
    const cursor = await Booking.find(condition).sort(order);
    if (!cursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = cursor.map((object) => {
      return {
        ...object._doc,
        _id: object.id,
        hotel: object.hotel.toString(),
        employees: object.employees.map((employee) => employee.toString()),
        guest: object.guest.toString(),
        plan: object.plan.toString(),
        agent: object.agent.toString(),
        rooms: object.rooms.map((room) => {
          return {
            ...room._doc,
            _id: room.id,
            room: room.room.toString()
          };
        })
      };
    });

    spread.map(async (booking) => {
      let guestCount = 0;
      let breakfastGuestCount = 0;

      booking.rooms.map(async (room) => {
        const occupancyDate = new Date(room.occupancyDate);
        const occupancyYear = occupancyDate.getFullYear();
        const occupancyMonth = occupancyDate.getMonth();
        const occupancyDay = occupancyDate.getDate();

        if (occupancyYear === currentYear && occupancyMonth === currentMonth && occupancyDay === currentDay) {
          guestCount = guestCount + room.guestCount;
          breakfastGuestCount = breakfastGuestCount + room.breakfastGuestCount;
        }
      });

      data.push({ booking, guestCount, breakfastGuestCount, currentDate });
    });

    return data;
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// find a document by id from a collection
const mongoGetBreakfast = async (hotelId, employeeId, id) => {
  const FUNCTION_NAME = "mongoGetBreakfast";

  try {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    const currentDay = currentDate.getDate();

    const condition = {
      _id: id,
      status: { $nin: [ROOM_STATUS.CHECKED_OUT, ROOM_STATUS.ADVANCE_BOOKED] }
    };
    const cursor = await Booking.findOne(condition);
    if (!cursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    const spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employees: cursor.employees.map((employee) => employee.toString()),
      guest: cursor.guest.toString(),
      plan: cursor.plan.toString(),
      agent: cursor.agent.toString(),
      rooms: cursor.rooms.map((room) => {
        return {
          ...room._doc,
          _id: room.id,
          room: room.room.toString()
        };
      })
    };

    let guestCount = 0;
    let breakfastGuestCount = 0;

    spread.rooms.map(async (room) => {
      const occupancyDate = new Date(room.occupancyDate);
      const occupancyYear = occupancyDate.getFullYear();
      const occupancyMonth = occupancyDate.getMonth();
      const occupancyDay = occupancyDate.getDate();

      if (occupancyYear === currentYear && occupancyMonth === currentMonth && occupancyDay === currentDay) {
        guestCount = guestCount + room.guestCount;
        breakfastGuestCount = breakfastGuestCount + room.breakfastGuestCount;
      }
    });

    return { findBooking: spread, currentDate, guestCount, breakfastGuestCount };
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};

// modify a document of a collection
const mongoModBreakfast = async (hotelId, employeeId, id, guestCount) => {
  const FUNCTION_NAME = "mongoModBreakfast";

  try {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    const currentDay = currentDate.getDate();

    let condition = {
      _id: id,
      status: { $nin: [ROOM_STATUS.CHECKED_OUT, ROOM_STATUS.ADVANCE_BOOKED] }
    };
    let cursor = await Booking.findOne(condition);
    if (!cursor) throw new customError(ERR_MSG.BOOKING_NOT_EXISTS, ERR_CODE.NOT_EXISTS);
    let spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employees: cursor.employees.map((employee) => employee.toString()),
      guest: cursor.guest.toString(),
      plan: cursor.plan.toString(),
      agent: cursor.agent.toString(),
      rooms: cursor.rooms.map((room) => {
        return {
          ...room._doc,
          _id: room.id,
          room: room.room.toString()
        };
      })
    };

    const roomArray = [];
    let countInput = guestCount;
    let totalGuest = 0;
    let breakfastGuestCount = 0;

    for (const room of spread.rooms) {
      const occupancyDate = new Date(room.occupancyDate);
      const occupancyYear = occupancyDate.getFullYear();
      const occupancyMonth = occupancyDate.getMonth();
      const occupancyDay = occupancyDate.getDate();

      totalGuest = totalGuest + room.guestCount;

      if (occupancyYear === currentYear && occupancyMonth === currentMonth && occupancyDay === currentDay) {
        if (room.guestCount >= countInput) {
          breakfastGuestCount = countInput;
          countInput = 0;
        } else {
          breakfastGuestCount = countInput - room.guestCount;
          countInput = countInput - room.guestCount;
        }

        roomArray.push({
          room: room.room,
          guestCount: room.guestCount,
          extraPersonCount: room.extraPersonCount,
          extraBedCount: room.extraBedCount,
          discount: room.discount,
          tariff: room.tariff,
          cGSTPercentage: room.cGSTPercentage,
          cGSTAmount: room.cGSTAmount,
          sGSTPercentage: room.sGSTPercentage,
          sGSTAmount: room.sGSTAmount,
          occupancyDate: room.occupancyDate,
          actualAccommodation: room.actualAccommodation,
          actualTariff: room.actualTariff,
          actualExtraPersonTariff: room.actualExtraPersonTariff,
          actualExtraBedTariff: room.actualExtraBedTariff,
          actualMaxDiscount: room.actualMaxDiscount,
          breakfastGuestCount: breakfastGuestCount
        });
      } else {
        roomArray.push(room);
      }
    }

    const modObject = await Booking.findByIdAndUpdate(id, { rooms: roomArray });
    if (!modObject) throw new customError(ERR_MSG.BOOKING_NOT_SAVE, ERR_CODE.INTERNAL);

    condition = { hotel: hotelId, _id: id };
    cursor = await Booking.findOne(condition);
    spread = {
      ...cursor._doc,
      _id: cursor.id,
      hotel: cursor.hotel.toString(),
      employees: cursor.employees.map((employee) => employee.toString()),
      guest: cursor.guest.toString(),
      plan: cursor.plan.toString(),
      agent: cursor.agent.toString(),
      rooms: cursor.rooms.map((room) => {
        return {
          ...room._doc,
          _id: room.id,
          room: room.room.toString()
        };
      })
    };

    return { booking: spread, currentDate, totalGuest, guestCount };
  } catch (error) {
    writeErrLog(hotelId, employeeId, FILE_NAME, FUNCTION_NAME, error.message);
    throw new customError(error.message, error.code);
  }
};


module.exports = { mongoBreakfasts, mongoGetBreakfast, mongoModBreakfast };