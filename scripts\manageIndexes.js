#!/usr/bin/env node

require('dotenv').config();
const { connectMongo } = require('../helpers/mongoClient');
const { initializeAllIndexes, checkAllIndexes } = require('../helpers/indexManager');
const { createMongoIndex, dropMongoIndex } = require('../helpers/createMongoIndex');
const { createRedisIndex, dropRedisIndex } = require('../helpers/createRedisIndex');


const command = process.argv[2];

const showHelp = () => {
    console.log(`
🔍 Search Index Management CLI

Usage: node scripts/manageIndexes.js <command>

Commands:
  init          Initialize all search indexes
  check         Check status of all search indexes
  create-mongo  Create MongoDB vector search index
  drop-mongo    Drop MongoDB vector search index
  create-redis  Create Redis vector search index
  drop-redis    Drop Redis vector search index
  help          Show this help message

Examples:
  node scripts/manageIndexes.js init
  node scripts/manageIndexes.js check
  node scripts/manageIndexes.js create-mongo
    `);
};

const main = async () => {
    try {
        if (!command || command === 'help') {
            showHelp();
            return;
        }

        // Connect to MongoDB first
        console.log('🔌 Connecting to MongoDB...');
        const connected = await connectMongo();
        if (!connected) {
            console.error('❌ Failed to connect to MongoDB');
            process.exit(1);
        }

        switch (command) {
            case 'init':
                console.log('🚀 Initializing all search indexes...');
                const success = await initializeAllIndexes();
                if (success) {
                    console.log('✅ All indexes initialized successfully');
                } else {
                    console.error('❌ Some indexes failed to initialize');
                    process.exit(1);
                }

                break;

            case 'check':
                console.log('🔍 Checking all search indexes...');
                await checkAllIndexes();

                break;

            case 'create-mongo':
                await createMongoIndex(process.env.INDEX_GST_FILTER);

                await createMongoIndex(process.env.INDEX_ROLE_FILTER);
                await createMongoIndex(process.env.INDEX_ROLE_TEXT);

                await createMongoIndex(process.env.INDEX_HOTEL_FILTER);
                await createMongoIndex(process.env.INDEX_HOTEL_TEXT);

                await createMongoIndex(process.env.INDEX_EMPLOYEE_FILTER);
                await createMongoIndex(process.env.INDEX_EMPLOYEE_TEXT);

                await createMongoIndex(process.env.INDEX_PLAN_FILTER);
                await createMongoIndex(process.env.INDEX_PLAN_TEXT);

                await createMongoIndex(process.env.INDEX_AGENT_FILTER);
                await createMongoIndex(process.env.INDEX_AGENT_TEXT);

                await createMongoIndex(process.env.INDEX_ID_CARD_FILTER);
                await createMongoIndex(process.env.INDEX_ID_CARD_TEXT);

                await createMongoIndex(process.env.INDEX_PAYMENT_MODE_FILTER);
                await createMongoIndex(process.env.INDEX_PAYMENT_MODE_TEXT);

                await createMongoIndex(process.env.INDEX_ROOM_CATEGORY_FILTER);
                await createMongoIndex(process.env.INDEX_ROOM_CATEGORY_TEXT);

                await createMongoIndex(process.env.INDEX_FOOD_FILTER);
                await createMongoIndex(process.env.INDEX_FOOD_TEXT);

                await createMongoIndex(process.env.INDEX_SERVICE_FILTER);
                await createMongoIndex(process.env.INDEX_SERVICE_TEXT);

                await createMongoIndex(process.env.INDEX_MISCELLANEOUS_FILTER);
                await createMongoIndex(process.env.INDEX_MISCELLANEOUS_TEXT);

                await createMongoIndex(process.env.INDEX_ROOM_HOTEL_FILTER);
                await createMongoIndex(process.env.INDEX_ROOM_TEXT);

                await createMongoIndex(process.env.INDEX_TABLE_FILTER);
                await createMongoIndex(process.env.INDEX_TABLE_TEXT);

                break;

            case 'drop-mongo':
                await dropMongoIndex(process.env.INDEX_GST_FILTER);

                await dropMongoIndex(process.env.INDEX_ROLE_FILTER);
                await dropMongoIndex(process.env.INDEX_ROLE_TEXT);

                await dropMongoIndex(process.env.INDEX_HOTEL_FILTER);
                await dropMongoIndex(process.env.INDEX_HOTEL_TEXT);

                await dropMongoIndex(process.env.INDEX_EMPLOYEE_FILTER);
                await dropMongoIndex(process.env.INDEX_EMPLOYEE_TEXT);

                await dropMongoIndex(process.env.INDEX_PLAN_FILTER);
                await dropMongoIndex(process.env.INDEX_PLAN_TEXT);

                await dropMongoIndex(process.env.INDEX_AGENT_FILTER);
                await dropMongoIndex(process.env.INDEX_AGENT_TEXT);

                await dropMongoIndex(process.env.INDEX_ID_CARD_FILTER);
                await dropMongoIndex(process.env.INDEX_ID_CARD_TEXT);

                await dropMongoIndex(process.env.INDEX_PAYMENT_MODE_FILTER);
                await dropMongoIndex(process.env.INDEX_PAYMENT_MODE_TEXT);

                await dropMongoIndex(process.env.INDEX_ROOM_CATEGORY_FILTER);
                await dropMongoIndex(process.env.INDEX_ROOM_CATEGORY_TEXT);

                await dropMongoIndex(process.env.INDEX_FOOD_FILTER);
                await dropMongoIndex(process.env.INDEX_FOOD_TEXT);

                await dropMongoIndex(process.env.INDEX_SERVICE_FILTER);
                await dropMongoIndex(process.env.INDEX_SERVICE_TEXT);

                await dropMongoIndex(process.env.INDEX_MISCELLANEOUS_FILTER);
                await dropMongoIndex(process.env.INDEX_MISCELLANEOUS_TEXT);

                await dropMongoIndex(process.env.INDEX_ROOM_HOTEL_FILTER);
                await dropMongoIndex(process.env.INDEX_ROOM_TEXT);

                await dropMongoIndex(process.env.INDEX_TABLE_FILTER);
                await dropMongoIndex(process.env.INDEX_TABLE_TEXT);

                break;

            case 'create-redis':
                await createRedisIndex(process.env.INDEX_GST_FILTER);

                await createRedisIndex(process.env.INDEX_ROLE_UNIQUE);
                await createRedisIndex(process.env.INDEX_ROLE_FILTER);

                await createRedisIndex(process.env.INDEX_HOTEL_UNIQUE);
                await createRedisIndex(process.env.INDEX_HOTEL_FILTER);

                await createRedisIndex(process.env.INDEX_EMPLOYEE_UNIQUE);
                await createRedisIndex(process.env.INDEX_EMPLOYEE_FILTER);

                await createRedisIndex(process.env.INDEX_PLAN_UNIQUE);
                await createRedisIndex(process.env.INDEX_PLAN_FILTER);

                await createRedisIndex(process.env.INDEX_AGENT_UNIQUE);
                await createRedisIndex(process.env.INDEX_AGENT_FILTER);

                await createRedisIndex(process.env.INDEX_ID_CARD_UNIQUE);
                await createRedisIndex(process.env.INDEX_ID_CARD_FILTER);

                await createRedisIndex(process.env.INDEX_PAYMENT_MODE_UNIQUE);
                await createRedisIndex(process.env.INDEX_PAYMENT_MODE_FILTER);

                await createRedisIndex(process.env.INDEX_ROOM_CATEGORY_UNIQUE);
                await createRedisIndex(process.env.INDEX_ROOM_CATEGORY_FILTER);

                await createRedisIndex(process.env.INDEX_FOOD_UNIQUE);
                await createRedisIndex(process.env.INDEX_FOOD_FILTER);

                await createRedisIndex(process.env.INDEX_SERVICE_UNIQUE);
                await createRedisIndex(process.env.INDEX_SERVICE_FILTER);

                await createRedisIndex(process.env.INDEX_MISCELLANEOUS_UNIQUE);
                await createRedisIndex(process.env.INDEX_MISCELLANEOUS_FILTER);

                await createRedisIndex(process.env.INDEX_ROOM_UNIQUE);
                await createRedisIndex(process.env.INDEX_ROOM_HOTEL_FILTER);

                await createRedisIndex(process.env.INDEX_TABLE_UNIQUE);
                await createRedisIndex(process.env.INDEX_TABLE_FILTER);

                break;

            case 'drop-redis':
                await dropRedisIndex(process.env.INDEX_GST_FILTER);

                await dropRedisIndex(process.env.INDEX_ROLE_UNIQUE);
                await dropRedisIndex(process.env.INDEX_ROLE_FILTER);

                await dropRedisIndex(process.env.INDEX_HOTEL_UNIQUE);
                await dropRedisIndex(process.env.INDEX_HOTEL_FILTER);

                await dropRedisIndex(process.env.INDEX_EMPLOYEE_UNIQUE);
                await dropRedisIndex(process.env.INDEX_EMPLOYEE_FILTER);

                await dropRedisIndex(process.env.INDEX_PLAN_UNIQUE);
                await dropRedisIndex(process.env.INDEX_PLAN_FILTER);

                await dropRedisIndex(process.env.INDEX_AGENT_UNIQUE);
                await dropRedisIndex(process.env.INDEX_AGENT_FILTER);

                await dropRedisIndex(process.env.INDEX_ID_CARD_UNIQUE);
                await dropRedisIndex(process.env.INDEX_ID_CARD_FILTER);

                await dropRedisIndex(process.env.INDEX_PAYMENT_MODE_UNIQUE);
                await dropRedisIndex(process.env.INDEX_PAYMENT_MODE_FILTER);

                await dropRedisIndex(process.env.INDEX_ROOM_CATEGORY_UNIQUE);
                await dropRedisIndex(process.env.INDEX_ROOM_CATEGORY_FILTER);

                await dropRedisIndex(process.env.INDEX_FOOD_UNIQUE);
                await dropRedisIndex(process.env.INDEX_FOOD_FILTER);

                await dropRedisIndex(process.env.INDEX_SERVICE_UNIQUE);
                await dropRedisIndex(process.env.INDEX_SERVICE_FILTER);

                await dropRedisIndex(process.env.INDEX_MISCELLANEOUS_UNIQUE);
                await dropRedisIndex(process.env.INDEX_MISCELLANEOUS_FILTER);

                await dropRedisIndex(process.env.INDEX_ROOM_UNIQUE);
                await dropRedisIndex(process.env.INDEX_ROOM_HOTEL_FILTER);

                await dropRedisIndex(process.env.INDEX_TABLE_UNIQUE);
                await dropRedisIndex(process.env.INDEX_TABLE_FILTER);

                break;

            default:
                console.error(`❌ Unknown command: ${command}`);
                showHelp();
                process.exit(1);
        }

        console.log('✅ Operation completed successfully');
        process.exit(0);

    } catch (error) {
        console.error('❌ Error:', error.message);
        process.exit(1);
    }
};

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down gracefully...');
    process.exit(0);
});


main();
