const { stringToBoolean } = require('./boolean');
const { initMongoIndex, checkMongoIndex } = require('./createMongoIndex');
const { initRedisIndex, checkRedisIndex } = require('./createRedisIndex');


// Initialize all search indexes based on configuration
const initializeAllIndexes = async () => {
    console.log('🚀 Starting search index initialization...');

    const results = {
        mongodb: false,
        redis: false
    };

    try {
        // Initialize MongoDB Atlas Vector Search Index
        console.log('📊 Initializing MongoDB index...');
        results.mongodb = await initMongoIndex();

        // Initialize Redis Search Index if enabled
        if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
            results.redis = await initRedisIndex();
        } else {
            results.redis = true;
        }

        // Summary
        console.log('📋 Index initialization summary:');
        console.log(`   MongoDB Index: ${results.mongodb ? '✅ Success' : '❌ Failed'}`);
        console.log(`   Redis Index: ${results.redis ? '✅ Success' : '❌ Failed'}`);

        return results.mongodb && results.redis;

    } catch (error) {
        console.error('❌ Error during index initialization:', error.message);
        return false;
    }
};

// Check status of all search indexes
const checkAllIndexes = async () => {
    console.log('🔍 Checking search index status...');

    const status = {
        mongodb: false,
        redis: false
    };

    try {
        // Check MongoDB Atlas Vector Search Index
        console.log('📊 Checking MongoDB vector search index...');
        status.mongodb = await checkMongoIndex();

        // Check Redis Search Index if enabled
        if (stringToBoolean(process.env.DB_REDIS_ENABLED)) {
            console.log('🔍 Checking Redis vector search index...');

            status.redis = await checkRedisIndex(process.env.INDEX_GST_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_GST_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_ROLE_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_ROLE_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_HOTEL_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_HOTEL_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_EMPLOYEE_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_EMPLOYEE_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_PLAN_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_PLAN_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_AGENT_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_AGENT_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_ID_CARD_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_ID_CARD_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_PAYMENT_MODE_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_PAYMENT_MODE_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_ROOM_CATEGORY_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_ROOM_CATEGORY_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_FOOD_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_FOOD_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_SERVICE_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_SERVICE_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_MISCELLANEOUS_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_MISCELLANEOUS_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_ROOM_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_ROOM_HOTEL_FILTER);

            status.redis = await checkRedisIndex(process.env.INDEX_TABLE_UNIQUE);
            status.redis = await checkRedisIndex(process.env.INDEX_TABLE_FILTER);
        } else {
            console.log('ℹ️  Redis is disabled');
            status.redis = true; // Mark as OK since it's disabled
        }

        // Summary
        console.log('📋 Index status summary:');
        console.log(`   MongoDB Index: ${status.mongodb ? '✅ Active' : '❌ Missing'}`);
        console.log(`   Redis Vector Index: ${status.redis ? '✅ Active' : '❌ Missing'}`);

        return status;

    } catch (error) {
        console.error('❌ Error checking index status:', error.message);
        return status;
    }
};

// Health check for search functionality
const searchHealthCheck = async () => {
    try {
        const indexStatus = await checkAllIndexes();

        const health = {
            status: 'OK',
            timestamp: new Date().toISOString(),
            indexes: {
                mongodb_vector: indexStatus.mongodb ? 'Active' : 'Missing',
                redis_vector: indexStatus.redis ? 'Active' : 'Missing'
            },
            search_ready: indexStatus.mongodb || indexStatus.redis
        };

        return health;

    } catch (error) {
        return {
            status: 'ERROR',
            timestamp: new Date().toISOString(),
            error: error.message,
            search_ready: false
        };
    }
};


module.exports = { initializeAllIndexes, checkAllIndexes, searchHealthCheck };
